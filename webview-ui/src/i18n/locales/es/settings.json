{"common": {"save": "Guardar", "done": "<PERSON><PERSON>", "cancel": "<PERSON><PERSON><PERSON>", "reset": "Restablecer", "select": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "add": "<PERSON><PERSON><PERSON>", "remove": "Eliminar"}, "header": {"title": "Configuración", "saveButtonTooltip": "Guardar cambios", "nothingChangedTooltip": "Nada ha cambiado", "doneButtonTooltip": "Descartar cambios no guardados y cerrar el panel de configuración"}, "unsavedChangesDialog": {"title": "Cambios no guardados", "description": "¿Desea descartar los cambios y continuar?", "cancelButton": "<PERSON><PERSON><PERSON>", "discardButton": "Descartar cambios"}, "sections": {"providers": "<PERSON>veed<PERSON>", "autoApprove": "Auto-aprobación", "browser": "Acceso al ordenador", "checkpoints": "Puntos de control", "notifications": "Notificaciones", "contextManagement": "Contexto", "terminal": "Terminal", "prompts": "Indicaciones", "completion": "Completado", "experimental": "Experimental", "language": "Idioma", "about": "Acerca de Zhanlu", "interface": "Interfaz"}, "developerMode": {"title": "<PERSON><PERSON> desarrollador", "label": "Activar modo desarrollador", "description": "El modo desarrollador proporciona funciones avanzadas y opciones de configuración, incluyendo funciones experimentales, configuración del terminal, gestión de indicaciones y más."}, "prompts": {"description": "Configura indicaciones de soporte que se utilizan para acciones rápidas como mejorar indicaciones, explicar código y solucionar problemas. Estas indicaciones ayudan a zhanlu a brindar mejor asistencia para tareas comunes de desarrollo."}, "codeIndex": {"title": "Indexación de código", "description": "Configura los ajustes de indexación de código para habilitar búsqueda semántica en tu proyecto. <0>Más información</0>", "statusTitle": "Estado", "enableLabel": "Habilitar indexación de código", "enableDescription": "Habilita la indexación de código para mejorar la búsqueda y la comprensión del contexto", "settingsTitle": "Configuración de indexación", "disabledMessage": "La indexación de código está actualmente deshabilitada. Habilítala en la configuración global para configurar las opciones de indexación.", "providerLabel": "Proveedor de embeddings", "embedderProviderLabel": "<PERSON><PERSON><PERSON><PERSON> de embedder", "selectProviderPlaceholder": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "openaiProvider": "OpenAI", "ollamaProvider": "Ollama", "geminiProvider": "Gemini", "geminiApiKeyLabel": "Clave API:", "geminiApiKeyPlaceholder": "Introduce tu clave de API de Gemini", "mistralProvider": "<PERSON><PERSON><PERSON>", "mistralApiKeyLabel": "Clave API:", "mistralApiKeyPlaceholder": "Introduce tu clave de API de Mistral", "openaiCompatibleProvider": "Compatible con OpenAI", "openAiKeyLabel": "Clave API de OpenAI", "openAiKeyPlaceholder": "Introduce tu clave API de OpenAI", "openAiCompatibleBaseUrlLabel": "URL base", "openAiCompatibleApiKeyLabel": "Clave API", "openAiCompatibleApiKeyPlaceholder": "Introduce tu clave API", "openAiCompatibleModelDimensionLabel": "Dimensión de Embedding:", "modelDimensionLabel": "Dimensión del modelo", "openAiCompatibleModelDimensionPlaceholder": "ej., 1536", "openAiCompatibleModelDimensionDescription": "La dimensión de embedding (tamaño de salida) para tu modelo. Consulta la documentación de tu proveedor para este valor. Valores comunes: 384, 768, 1536, 3072.", "modelLabel": "<PERSON><PERSON>", "modelPlaceholder": "Introduce el nombre del modelo", "selectModel": "Seleccionar un modelo", "selectModelPlaceholder": "Seleccionar modelo", "ollamaUrlLabel": "URL de Ollama:", "ollamaBaseUrlLabel": "URL base de Ollama", "qdrantUrlLabel": "URL de Qdrant", "qdrantKeyLabel": "<PERSON><PERSON><PERSON>:", "qdrantApiKeyLabel": "Clave API de Qdrant", "qdrantApiKeyPlaceholder": "Introduce tu clave API de Qdrant (opcional)", "setupConfigLabel": "Configuración", "startIndexingButton": "Iniciar", "clearIndexDataButton": "<PERSON><PERSON><PERSON>", "unsavedSettingsMessage": "Por favor guarda tus ajustes antes de iniciar el proceso de indexación.", "clearDataDialog": {"title": "¿Estás seguro?", "description": "Esta acción no se puede deshacer. Esto eliminará permanentemente los datos de índice de tu base de código.", "cancelButton": "<PERSON><PERSON><PERSON>", "confirmButton": "<PERSON><PERSON><PERSON> da<PERSON>"}, "ollamaUrlPlaceholder": "http://localhost:11434", "openAiCompatibleBaseUrlPlaceholder": "https://api.example.com", "modelDimensionPlaceholder": "1536", "qdrantUrlPlaceholder": "http://localhost:6333", "saveError": "Error al guardar la configuración", "modelDimensions": "({{dimension}} dimensiones)", "saveSuccess": "Configuración guardada exitosamente", "saving": "Guardando...", "saveSettings": "Guardar", "indexingStatuses": {"standby": "En espera", "indexing": "Indexando", "indexed": "Indexado", "error": "Error"}, "close": "<PERSON><PERSON><PERSON>", "validation": {"invalidQdrantUrl": "URL de Qdrant no válida", "invalidOllamaUrl": "URL de Ollama no válida", "invalidBaseUrl": "URL base no válida", "qdrantUrlRequired": "Se requiere la URL de Qdrant", "openaiApiKeyRequired": "Se requiere la clave API de OpenAI", "modelSelectionRequired": "Se requiere la selección de un modelo", "apiKeyRequired": "Se requiere la clave API", "modelIdRequired": "Se requiere el ID del modelo", "modelDimensionRequired": "Se requiere la dimensión del modelo", "geminiApiKeyRequired": "Se requiere la clave API de Gemini", "mistralApiKeyRequired": "Se requiere la clave de API de Mistral", "ollamaBaseUrlRequired": "Se requiere la URL base de Ollama", "baseUrlRequired": "Se requiere la URL base", "modelDimensionMinValue": "La dimensión del modelo debe ser mayor que 0"}, "advancedConfigLabel": "Configuración avanzada", "searchMinScoreLabel": "Umbral de puntuación de búsqueda", "searchMinScoreDescription": "Puntuación mínima de similitud (0.0-1.0) requerida para los resultados de búsqueda. Valores más bajos devuelven más resultados pero pueden ser menos relevantes. Valores más altos devuelven menos resultados pero más relevantes.", "searchMinScoreResetTooltip": "Restablecer al valor predeterminado (0.4)", "searchMaxResultsLabel": "Resultados máximos de búsqueda", "searchMaxResultsDescription": "Número máximo de resultados de búsqueda a devolver al consultar el índice de código. Valores más altos proporcionan más contexto pero pueden incluir resultados menos relevantes.", "resetToDefault": "Restablecer al valor predeterminado"}, "autoApprove": {"description": "Permitir que zhanlu realice operaciones automáticamente sin requerir aprobación. Habilite esta configuración solo si confía plenamente en la IA y comprende los riesgos de seguridad asociados.", "toggleAriaLabel": "Alternar aprobación automática", "disabledAriaLabel": "Aprobación automática desactivada: seleccione primero las opciones", "readOnly": {"label": "Lectura", "description": "<PERSON>uando está habilitado, <PERSON><PERSON><PERSON> verá automáticamente el contenido del directorio y leerá archivos sin que necesite hacer clic en el botón Aprobar.", "outsideWorkspace": {"label": "Incluir archivos fuera del espacio de trabajo", "description": "Per<PERSON><PERSON> leer archivos fuera del espacio de trabajo actual sin requerir aprobación."}}, "write": {"label": "Escritura", "description": "Crear y editar archivos automáticamente sin requerir aprobación", "delayLabel": "Retraso después de escritura para permitir que los diagnósticos detecten posibles problemas", "outsideWorkspace": {"label": "Incluir archivos fuera del espacio de trabajo", "description": "Permitir a <PERSON> crear y editar archivos fuera del espacio de trabajo actual sin requerir aprobación."}, "protected": {"label": "Incluir archivos protegidos", "description": "Permitir a <PERSON> crear y editar archivos protegidos (como .zhanluignore y archivos de configuración .zhanlu/) sin requerir aprobación."}}, "browser": {"label": "<PERSON><PERSON><PERSON><PERSON>", "description": "Realizar acciones del navegador automáticamente sin requerir aprobación. Nota: Solo se aplica cuando el modelo admite el uso del ordenador"}, "retry": {"label": "Reintentar", "description": "Reintentar automáticamente solicitudes de API fallidas cuando el servidor devuelve una respuesta de error", "delayLabel": "Retraso antes de reintentar la solicitud"}, "mcp": {"label": "MCP", "description": "Habilitar la aprobación automática de herramientas MCP individuales en la vista de Servidores MCP (requiere tanto esta configuración como la casilla \"Permitir siempre\" de la herramienta)"}, "modeSwitch": {"label": "Modo", "description": "Cambiar automáticamente entre diferentes modos sin requerir aprobación"}, "subtasks": {"label": "Subtareas", "description": "Permitir la creación y finalización de subtareas sin requerir aprobación"}, "followupQuestions": {"label": "Pregunta", "description": "Seleccionar automáticamente la primera respuesta sugerida para preguntas de seguimiento después del tiempo de espera configurado", "timeoutLabel": "Tiempo de espera antes de seleccionar automáticamente la primera respuesta"}, "execute": {"label": "<PERSON><PERSON><PERSON><PERSON>", "description": "Ejecutar automáticamente comandos de terminal permitidos sin requerir aprobación", "allowedCommands": "Comandos de auto-ejecución permitidos", "allowedCommandsDescription": "Prefijos de comandos que pueden ser ejecutados automáticamente cuando \"Aprobar siempre operaciones de ejecución\" está habilitado. Añade * para permitir todos los comandos (usar con precaución).", "deniedCommands": "Comand<PERSON> den<PERSON>", "deniedCommandsDescription": "Prefijos de comandos que serán automáticamente denegados sin pedir aprobación. En caso de conflictos con comandos permitidos, la coincidencia de prefijo más larga tiene prioridad. Añade * para denegar todos los comandos.", "commandPlaceholder": "Ingrese prefijo de comando (ej. 'git ')", "deniedCommandPlaceholder": "Ingrese prefijo de comando a denegar (ej. 'rm -rf')", "addButton": "<PERSON><PERSON><PERSON>", "autoDenied": "Los comandos con el prefijo `{{prefix}}` han sido prohibidos por el usuario. No eludes esta restricción ejecutando otro comando."}, "updateTodoList": {"label": "Todo", "description": "La lista de tareas se actualiza automáticamente sin aprobación"}, "apiRequestLimit": {"title": "Solicitudes máximas", "description": "Realizar automáticamente esta cantidad de solicitudes a la API antes de pedir aprobación para continuar con la tarea.", "unlimited": "<PERSON><PERSON><PERSON><PERSON>"}, "selectOptionsFirst": "Selecciona al menos una opción a continuación para habilitar la aprobación automática"}, "providers": {"providerDocumentation": "Documentación de {{provider}}", "configProfile": "Perfil de configuración", "description": "Guarde diferentes configuraciones de API para cambiar rápidamente entre proveedores y ajustes.", "apiProvider": "Proveedor de API", "model": "<PERSON><PERSON>", "nameEmpty": "El nombre no puede estar vacío", "nameExists": "Ya existe un perfil con este nombre", "nameTooLong": "El nombre no puede exceder 20 caracteres", "deleteProfile": "Eliminar perfil", "invalidArnFormat": "Formato de ARN no válido. Verifica los ejemplos anteriores.", "enterNewName": "Ingrese un nuevo nombre", "addProfile": "Agregar perfil", "renameProfile": "Renombrar perfil", "newProfile": "Nuevo perfil de configuración", "enterProfileName": "Ingrese el nombre del perfil", "createProfile": "<PERSON><PERSON><PERSON> perfil", "cannotDeleteOnlyProfile": "No se puede eliminar el único perfil", "searchPlaceholder": "Buscar perfiles", "searchProviderPlaceholder": "Buscar proveedores", "noProviderMatchFound": "No se encontraron proveedores", "noMatchFound": "No se encontraron perfiles coincidentes", "vscodeLmDescription": "La API del Modelo de Lenguaje de VS Code le permite ejecutar modelos proporcionados por otras extensiones de VS Code (incluido, entre otros, GitHub Copilot). La forma más sencilla de empezar es instalar las extensiones Copilot y Copilot Chat desde el VS Code Marketplace.", "awsCustomArnUse": "Ingrese un ARN de Amazon Bedrock válido para el modelo que desea utilizar. Ejemplos de formato:", "awsCustomArnDesc": "Asegúrese de que la región en el ARN coincida con la región de AWS seleccionada anteriormente.", "openRouterApiKey": "Clave API de OpenRouter", "getOpenRouterApiKey": "Obtener clave API de OpenRouter", "apiKeyStorageNotice": "Las claves API se almacenan de forma segura en el Almacenamiento Secreto de VSCode", "glamaApiKey": "Clave API de Glama", "getGlamaApiKey": "Obtener clave API de Glama", "useCustomBaseUrl": "Usar URL base personalizada", "useReasoning": "Habilitar razonamiento", "useHostHeader": "Usar encabezado Host personalizado", "useLegacyFormat": "Usar formato API de OpenAI heredado", "customHeaders": "Encabezados personalizados", "headerName": "Nombre del encabezado", "headerValue": "Valor del encabezado", "noCustomHeaders": "No hay encabezados personalizados definidos. Haga clic en el botón + para añadir uno.", "requestyApiKey": "Clave API de Requesty", "refreshModels": {"label": "Actualizar modelos", "hint": "Por favor, vuelve a abrir la configuración para ver los modelos más recientes.", "loading": "Actualizando lista de modelos...", "success": "¡Lista de modelos actualizada correctamente!", "error": "Error al actualizar la lista de modelos. Por favor, inténtalo de nuevo."}, "getRequestyApiKey": "Obtener clave API de Requesty", "openRouterTransformsText": "Comprimir prompts y cadenas de mensajes al tamaño del contexto (<a>Transformaciones de OpenRouter</a>)", "anthropicApiKey": "Clave API de Zhanlu", "getAnthropicApiKey": "Obtener clave API de Zhanlu", "anthropicUseAuthToken": "Pasar la clave API de Anthropic como encabezado de autorización en lugar de X-Api-Key", "chutesApiKey": "Clave API de Chutes", "getChutesApiKey": "Obtener clave API de Chutes", "deepSeekApiKey": "Clave API de DeepSeek", "getDeepSeekApiKey": "Obtener clave API de DeepSeek", "moonshotApiKey": "Clave API de Moonshot", "getMoonshotApiKey": "Obtener clave API de Moonshot", "moonshotBaseUrl": "Punto de entrada de Moonshot", "geminiApiKey": "Clave API de Gemini", "getGroqApiKey": "Obtener clave API de Groq", "groqApiKey": "Clave API de Groq", "getHuggingFaceApiKey": "Obtener clave API de Hugging Face", "huggingFaceApiKey": "Clave API de Hugging Face", "huggingFaceModelId": "ID del modelo", "huggingFaceLoading": "Cargando...", "huggingFaceModelsCount": "({{count}} modelos)", "huggingFaceSelectModel": "Seleccionar un modelo...", "huggingFaceSearchModels": "Buscar modelos...", "huggingFaceNoModelsFound": "No se encontraron modelos", "huggingFaceProvider": "<PERSON><PERSON><PERSON><PERSON>", "huggingFaceProviderAuto": "Automático", "huggingFaceSelectProvider": "<PERSON><PERSON><PERSON><PERSON>r un proveedor...", "huggingFaceSearchProviders": "Buscar proveedores...", "huggingFaceNoProvidersFound": "No se encontraron proveedores", "getGeminiApiKey": "Obtener clave API de Gemini", "openAiApiKey": "Clave API de OpenAI", "apiKey": "Clave API", "openAiBaseUrl": "URL base", "getOpenAiApiKey": "Obtener clave API de OpenAI", "mistralApiKey": "Clave API de Mistral", "getMistralApiKey": "Obtener clave API de Mistral / Codestral", "codestralBaseUrl": "URL base de Codestral (Opcional)", "codestralBaseUrlDesc": "Establecer una URL alternativa para el modelo Codestral.", "xaiApiKey": "Clave API de xAI", "getXaiApiKey": "Obtener clave API de xAI", "litellmApiKey": "Clave API de LiteLLM", "litellmBaseUrl": "URL base de LiteLLM", "awsCredentials": "Credenciales de AWS", "awsProfile": "Perfil de AWS", "awsApiKey": "Clave de API de Amazon Bedrock", "awsProfileName": "Nombre del perfil de AWS", "awsAccessKey": "Clave de acceso de AWS", "awsSecretKey": "Clave secreta de AWS", "awsSessionToken": "Token de sesión de AWS", "awsRegion": "Región de AWS", "awsCrossRegion": "Usar inferencia entre regiones", "awsBedrockVpc": {"useCustomVpcEndpoint": "Usar punto de conexión VPC personalizado", "vpcEndpointUrlPlaceholder": "Ingrese URL del punto de conexión VPC (opcional)", "examples": "Ejemplos:"}, "enablePromptCaching": "Habilitar caché de prompts", "enablePromptCachingTitle": "Habilitar el caché de prompts para mejorar el rendimiento y reducir costos para modelos compatibles.", "cacheUsageNote": "Nota: Si no ve el uso del caché, intente seleccionar un modelo diferente y luego seleccionar nuevamente su modelo deseado.", "vscodeLmModel": "<PERSON><PERSON>ng<PERSON>", "vscodeLmWarning": "Nota: Esta es una integración muy experimental y el soporte del proveedor variará. Si recibe un error sobre un modelo no compatible, es un problema del proveedor.", "googleCloudSetup": {"title": "Para usar Google Cloud Vertex AI, necesita:", "step1": "1. <PERSON><PERSON>r una cuenta de Google Cloud, habilitar la API de Vertex AI y habilitar los modelos Zhanlu deseados.", "step2": "2. Instalar Google Cloud CLI y configurar las credenciales predeterminadas de la aplicación.", "step3": "3. O crear una cuenta de servicio con credenciales."}, "googleCloudCredentials": "Credenciales de Google Cloud", "googleCloudKeyFile": "Ruta del archivo de clave de Google Cloud", "googleCloudProjectId": "ID del proyecto de Google Cloud", "googleCloudRegion": "Región de Google Cloud", "lmStudio": {"baseUrl": "URL base (opcional)", "modelId": "ID del modelo", "speculativeDecoding": "Habilitar decodificación especulativa", "draftModelId": "ID del modelo borrador", "draftModelDesc": "El modelo borrador debe ser de la misma familia de modelos para que la decodificación especulativa funcione correctamente.", "selectDraftModel": "Se<PERSON><PERSON><PERSON><PERSON>o borrador", "noModelsFound": "No se encontraron modelos borrador. Asegúrese de que LM Studio esté ejecutándose con el Modo Servidor habilitado.", "description": "LM Studio le permite ejecutar modelos localmente en su computadora. Para obtener instrucciones sobre cómo comenzar, consulte su <a>guía de inicio rápido</a>. También necesitará iniciar la función de <b>servidor local</b> de LM Studio para usarlo con esta extensión. <span>Nota:</span> <PERSON>hanlu utiliza prompts complejos y funciona mejor con modelos Z<PERSON>lu. Los modelos menos capaces pueden no funcionar como se espera."}, "ollama": {"baseUrl": "URL base (opcional)", "modelId": "ID del modelo", "description": "Ollama le permite ejecutar modelos localmente en su computadora. Para obtener instrucciones sobre cómo comenzar, consulte la guía de inicio rápido.", "warning": "Nota: <PERSON><PERSON><PERSON> utiliza prompts complejos y funciona mejor con model<PERSON>. Los modelos menos capaces pueden no funcionar como se espera."}, "unboundApiKey": "Clave API de Unbound", "getUnboundApiKey": "Obtener clave API de Unbound", "unboundRefreshModelsSuccess": "¡Lista de modelos actualizada! Ahora puede seleccionar entre los últimos modelos.", "unboundInvalidApiKey": "Clave API inválida. Por favor, verifique su clave API e inténtelo de nuevo.", "humanRelay": {"description": "No se requiere clave API, pero el usuario necesita ayudar a copiar y pegar la información en el chat web de IA.", "instructions": "Durante el uso, aparecerá un cuadro de diálogo y el mensaje actual se copiará automáticamente al portapapeles. Debe pegarlo en las versiones web de IA (como Zhanlu), luego copiar la respuesta de la IA de vuelta al cuadro de diálogo y hacer clic en el botón de confirmar."}, "openRouter": {"providerRouting": {"title": "Enrutamiento de Proveedores de OpenRouter", "description": "OpenRouter dirige las solicitudes a los mejores proveedores disponibles para su modelo. <PERSON>r defecto, las solicitudes se equilibran entre los principales proveedores para maximizar el tiempo de actividad. Sin embargo, puede elegir un proveedor específico para este modelo.", "learnMore": "Más información sobre el enrutamiento de proveedores"}}, "customModel": {"capabilities": "Configure las capacidades y precios para su modelo personalizado compatible con OpenAI. Tenga cuidado al especificar las capacidades del modelo, ya que pueden afectar cómo funciona Zhanlu.", "maxTokens": {"label": "Tokens máximos de salida", "description": "Número máximo de tokens que el modelo puede generar en una respuesta. (Especifique -1 para permitir que el servidor establezca los tokens máximos.)"}, "contextWindow": {"label": "Tamaño de ventana de contexto", "description": "Total de tokens (entrada + salida) que el modelo puede procesar."}, "imageSupport": {"label": "Soporte de imágenes", "description": "¿Es este modelo capaz de procesar y entender imágenes?"}, "computerUse": {"label": "Uso del ordenador", "description": "¿Es este modelo capaz de interactuar con un navegador? (ej. <PERSON>)"}, "promptCache": {"label": "<PERSON><PERSON><PERSON> de prompts", "description": "¿Es este modelo capaz de almacenar prompts en caché?"}, "pricing": {"input": {"label": "Precio de entrada", "description": "Costo por millón de tokens en la entrada/prompt. Esto afecta el costo de enviar contexto e instrucciones al modelo."}, "output": {"label": "<PERSON>cio de salida", "description": "Costo por millón de tokens en la respuesta del modelo. Esto afecta el costo del contenido generado y las completaciones."}, "cacheReads": {"label": "Precio de lecturas de caché", "description": "Costo por millón de tokens para leer del caché. Este es el precio que se cobra cuando se recupera una respuesta almacenada en caché."}, "cacheWrites": {"label": "Precio de escrituras de caché", "description": "Costo por millón de tokens para escribir en el caché. Este es el precio que se cobra cuando se almacena un prompt en caché por primera vez."}}, "resetDefaults": "Restablecer valores predeterminados"}, "rateLimitSeconds": {"label": "Límite de tasa", "description": "Tiempo mínimo entre solicitudes de API."}, "consecutiveMistakeLimit": {"label": "Límite de errores y repeticiones", "description": "Número de errores consecutivos o acciones repetidas antes de mostrar el diálogo 'Roo está teniendo problemas'", "unlimitedDescription": "Reintentos ilimitados habilitados (proceder automáticamente). El diálogo nunca aparecerá.", "warning": "⚠️ Establecer en 0 permite reintentos ilimitados que pueden consumir un uso significativo de la API"}, "reasoningEffort": {"label": "Esfuerzo de razonamiento del modelo", "high": "Alto", "medium": "Medio", "low": "<PERSON><PERSON>"}, "setReasoningLevel": "Habilitar esfuerzo de razonamiento", "claudeCode": {"pathLabel": "<PERSON><PERSON>", "description": "Ruta opcional a su CLI de Claude Code. Por defecto, es 'claude' si no se establece.", "placeholder": "Por defecto: claude", "maxTokensLabel": "Tokens máximos de salida", "maxTokensDescription": "Número máximo de tokens de salida para las respuestas de Claude Code. El valor predeterminado es 8000."}}, "browser": {"enable": {"label": "Habilitar herramienta <PERSON>", "description": "<PERSON><PERSON>do está habilitado, <PERSON><PERSON><PERSON> puede usar un navegador para interactuar con sitios web cuando se utilizan modelos que admiten el uso del ordenador."}, "viewport": {"label": "Tamaño del viewport", "description": "Seleccione el tamaño del viewport para interacciones del navegador. Esto afecta cómo se muestran e interactúan los sitios web.", "options": {"largeDesktop": "Escritorio grande (1280x800)", "smallDesktop": "Escritorio pequeño (900x600)", "tablet": "Tablet (768x1024)", "mobile": "Móvil (360x640)"}}, "screenshotQuality": {"label": "Calidad de capturas de pantalla", "description": "Ajuste la calidad WebP de las capturas de pantalla del navegador. Valores más altos proporcionan capturas más claras pero aumentan el uso de token."}, "remote": {"label": "Usar conexión remota del navegador", "description": "Conectarse a un navegador Chrome que se ejecuta con depuración remota habilitada (--remote-debugging-port=9222).", "urlPlaceholder": "URL personalizada (ej. http://localhost:9222)", "testButton": "Probar conexi<PERSON>", "testingButton": "Probando...", "instructions": "Ingrese la dirección del host del protocolo DevTools o déjelo vacío para descubrir automáticamente instancias locales de Chrome. El botón Probar Conexión intentará usar la URL personalizada si se proporciona, o descubrirá automáticamente si el campo está vacío."}}, "checkpoints": {"enable": {"label": "Habilitar puntos de control automáticos", "description": "<PERSON>uando está habilitado, <PERSON><PERSON><PERSON> creará automáticamente puntos de control durante la ejecución de tareas, facilitando la revisión de cambios o la reversión a estados anteriores."}}, "notifications": {"sound": {"label": "Habilitar efectos de sonido", "description": "<PERSON>uando está habilitado, <PERSON><PERSON><PERSON> reproducirá efectos de sonido para notificaciones y eventos.", "volumeLabel": "Volumen"}, "tts": {"label": "Habilitar texto a voz", "description": "<PERSON><PERSON>do está habilitado, <PERSON><PERSON><PERSON> leer<PERSON> en voz alta sus respuestas usando texto a voz.", "speedLabel": "Velocidad"}}, "contextManagement": {"description": "Controle qué información se incluye en la ventana de contexto de la IA, afectando el uso de token y la calidad de respuesta", "autoCondenseContextPercent": {"label": "Umbral para activar la condensación inteligente de contexto", "description": "<PERSON>uando la ventana de contexto alcanza este umbral, z<PERSON><PERSON> la condensará automáticamente."}, "condensingApiConfiguration": {"label": "Configuración de API para condensación de contexto", "description": "Seleccione qué configuración de API usar para operaciones de condensación de contexto. Deje sin seleccionar para usar la configuración activa actual.", "useCurrentConfig": "Usar configuración actual"}, "customCondensingPrompt": {"label": "Prompt personalizado para condensación de contexto", "description": "Personalice el prompt del sistema utilizado para la condensación de contexto. Deje vacío para usar el prompt predeterminado.", "placeholder": "Ingrese su prompt de condensación personalizado aquí...\n\nPuede usar la misma estructura que el prompt predeterminado:\n- Conversación anterior\n- Trabajo actual\n- Conceptos técnicos clave\n- Archivos y código relevantes\n- Resolución de problemas\n- Tareas pendientes y próximos pasos", "reset": "Restablecer a predeterminado", "hint": "Vacío = usar prompt predeterminado"}, "autoCondenseContext": {"name": "Activar automáticamente la condensación inteligente de contexto", "description": "<PERSON>uando está habilitado, zhan<PERSON> condensará automáticamente el contexto cuando se alcance el umbral. Cuando está deshabilitado, aún puedes activar manualmente la condensación de contexto."}, "openTabs": {"label": "Límite de contexto de pestañas abiertas", "description": "Número máximo de pestañas abiertas de VSCode a incluir en el contexto. Valores más altos proporcionan más contexto pero aumentan el uso de token."}, "workspaceFiles": {"label": "Límite de contexto de archivos del espacio de trabajo", "description": "Número máximo de archivos a incluir en los detalles del directorio de trabajo actual. Valores más altos proporcionan más contexto pero aumentan el uso de token."}, "rooignore": {"label": "Mostrar archivos .zhanluignore en listas y búsquedas", "description": "Cuando está habilitado, los archivos que coinciden con los patrones en .zhanluignore se mostrarán en listas con un símbolo de candado. Cuando está deshabilitado, estos archivos se ocultarán completamente de las listas de archivos y búsquedas."}, "maxReadFile": {"label": "Umbral de auto-truncado de lectura de archivos", "description": "<PERSON><PERSON><PERSON> lee este número de líneas cuando el modelo omite valores de inicio/fin. Si este número es menor que el total del archivo, Zhanlu genera un índice de números de línea de las definiciones de código. Casos especiales: -1 indica a Zhanlu que lea el archivo completo (sin indexación), y 0 indica que no lea líneas y proporcione solo índices de línea para un contexto mínimo. Valores más bajos minimizan el uso inicial de contexto, permitiendo lecturas posteriores de rangos de líneas precisos. Las solicitudes con inicio/fin explícitos no están limitadas por esta configuración.", "lines": "líneas", "always_full_read": "Siempre leer el archivo completo"}, "maxConcurrentFileReads": {"label": "Límite de lecturas simultáneas", "description": "Número máximo de archivos que la herramienta 'read_file' puede procesar simultáneamente. Valores más altos pueden acelerar la lectura de múltiples archivos pequeños pero aumentan el uso de memoria."}, "diagnostics": {"includeMessages": {"label": "Incluir automáticamente diagnósticos en el contexto", "description": "Cuando está habilitado, los mensajes de diagnóstico (errores) de los archivos editados se incluirán automáticamente en el contexto. Siempre puedes incluir manualmente todos los diagnósticos del espacio de trabajo usando @problems."}, "maxMessages": {"label": "Máximo de mensajes de diagnóstico", "description": "Número máximo de mensajes de diagnóstico a incluir por archivo. Este límite se aplica tanto a la inclusión automática (cuando la casilla está habilitada) como a las menciones manuales de @problems. Valores más altos proporcionan más contexto pero aumentan el uso de tokens.", "resetTooltip": "Restablecer al valor predeterminado (50)", "unlimitedLabel": "<PERSON><PERSON><PERSON><PERSON>"}, "delayAfterWrite": {"label": "Retraso después de escrituras para permitir que los diagnósticos detecten problemas potenciales", "description": "Tiempo de espera después de escrituras de archivos antes de continuar, permitiendo que las herramientas de diagnóstico procesen cambios y detecten problemas."}}, "condensingThreshold": {"label": "Umbral de condensación de contexto", "selectProfile": "Configurar umbral para perfil", "defaultProfile": "Predeterminado global (todos los perfiles)", "defaultDescription": "<PERSON>uando el contexto alcance este porcentaje, se condensará automáticamente para todos los perfiles a menos que tengan configuraciones personalizadas", "profileDescription": "Umbral personalizado solo para este perfil (anula el predeterminado global)", "inheritDescription": "Este perfil hereda el umbral predeterminado global ({{threshold}}%)", "usesGlobal": "(usa global {{threshold}}%)"}}, "terminal": {"basic": {"label": "Configuración del terminal: Básica", "description": "Configuración básica del terminal"}, "advanced": {"label": "Configuración del terminal: Avanzada", "description": "Las siguientes opciones pueden requerir reiniciar el terminal para aplicar la configuración."}, "outputLineLimit": {"label": "Límite de salida de terminal", "description": "Número máximo de líneas a incluir en la salida del terminal al ejecutar comandos. <PERSON><PERSON>do se excede, se eliminarán líneas del medio, ahorrando token. <0>Más información</0>"}, "outputCharacterLimit": {"label": "Límite de caracteres del terminal", "description": "Número máximo de caracteres a incluir en la salida del terminal al ejecutar comandos. Este límite tiene prioridad sobre el límite de líneas para evitar problemas de memoria con líneas extremadamente largas. Cuando se excede, la salida se truncará. <0>Aprende más</0>"}, "shellIntegrationTimeout": {"label": "Tiempo de espera de integración del shell del terminal", "description": "Tiempo máximo de espera para la inicialización de la integración del shell antes de ejecutar comandos. Para usuarios con tiempos de inicio de shell largos, este valor puede necesitar ser aumentado si ve errores \"Shell Integration Unavailable\" en el terminal. <0>Más información</0>"}, "shellIntegrationDisabled": {"label": "Desactivar la integración del shell del terminal", "description": "Activa esto si los comandos del terminal no funcionan correctamente o si ves errores de 'Shell Integration Unavailable'. Esto utiliza un método más simple para ejecutar comandos, omitiendo algunas funciones avanzadas del terminal. <0>Más información</0>"}, "commandDelay": {"label": "Retraso de comando del terminal", "description": "Retraso en milisegundos para añadir después de la ejecución del comando. La configuración predeterminada de 0 desactiva completamente el retraso. Esto puede ayudar a asegurar que la salida del comando se capture completamente en terminales con problemas de temporización. En la mayoría de terminales se implementa estableciendo `PROMPT_COMMAND='sleep N'` y Powershell añade `start-sleep` al final de cada comando. Originalmente era una solución para el error VSCode#237208 y puede no ser necesario. <0>Más información</0>"}, "compressProgressBar": {"label": "Comprimir salida de barras de progreso", "description": "Cuando está habilitado, procesa la salida del terminal con retornos de carro (\\r) para simular cómo un terminal real mostraría el contenido. Esto elimina los estados intermedios de las barras de progreso, conservando solo el estado final, lo que ahorra espacio de contexto para información más relevante. <0>Más información</0>"}, "powershellCounter": {"label": "Habilitar solución temporal del contador de PowerShell", "description": "Cuando está habilitado, agrega un contador a los comandos de PowerShell para garantizar la ejecución correcta de los comandos. Esto ayuda con las terminales PowerShell que pueden tener problemas con la captura de salida de comandos. <0>Más información</0>"}, "zshClearEolMark": {"label": "Limpiar marca de fin de línea de ZSH", "description": "Cuando está habilitado, limpia la marca de fin de línea de ZSH estableciendo PROMPT_EOL_MARK=''. Esto evita problemas con la interpretación de la salida de comandos cuando termina con caracteres especiales como '%'. <0>Más información</0>"}, "zshOhMy": {"label": "Habilitar integración Oh My Zsh", "description": "Cuando está habilitado, establece ITERM_SHELL_INTEGRATION_INSTALLED=Yes para habilitar las características de integración del shell Oh My Zsh. Aplicar esta configuración puede requerir reiniciar el IDE. <0>Más información</0>"}, "zshP10k": {"label": "Habilitar integración Powerlevel10k", "description": "Cuando está habilitado, establece POWERLEVEL9K_TERM_SHELL_INTEGRATION=true para habilitar las características de integración del shell Powerlevel10k. <0>Más información</0>"}, "zdotdir": {"label": "Habilitar gestión de ZDOTDIR", "description": "Cuando está habilitado, crea un directorio temporal para ZDOTDIR para manejar correctamente la integración del shell zsh. Esto asegura que la integración del shell de VSCode funcione correctamente con zsh mientras preserva tu configuración de zsh. <0>Más información</0>"}, "inheritEnv": {"label": "Heredar variables de entorno", "description": "Cuando está habilitado, el terminal hereda las variables de entorno del proceso padre de VSCode, como la configuración de integración del shell definida en el perfil del usuario. Esto alterna directamente la configuración global de VSCode `terminal.integrated.inheritEnv`. <0>Más información</0>"}}, "advancedSettings": {"title": "Configuración avanzada"}, "advanced": {"diff": {"label": "Habilitar edición a través de diffs", "description": "<PERSON>uando está habilitado, <PERSON><PERSON><PERSON> podrá editar archivos más rápidamente y rechazará automáticamente escrituras completas de archivos truncados. Funciona mejor con el último modelo Zhanlu.", "strategy": {"label": "Estrategia de diff", "options": {"standard": "Est<PERSON><PERSON> (Bloque único)", "multiBlock": "Experimental: Diff multi-bloque", "unified": "Experimental: <PERSON>ff unificado"}, "descriptions": {"standard": "La estrategia de diff estándar aplica cambios a un solo bloque de código a la vez.", "unified": "La estrategia de diff unificado toma múltiples enfoques para aplicar diffs y elige el mejor enfoque.", "multiBlock": "La estrategia de diff multi-bloque permite actualizar múltiples bloques de código en un archivo en una sola solicitud."}}, "matchPrecision": {"label": "Precisión de coincidencia", "description": "Este control deslizante controla cuán precisamente deben coincidir las secciones de código al aplicar diffs. Valores más bajos permiten coincidencias más flexibles pero aumentan el riesgo de reemplazos incorrectos. Use valores por debajo del 100% con extrema precaución."}}, "todoList": {"label": "Habilitar herramienta de lista de tareas", "description": "<PERSON>uando está habilitado, z<PERSON><PERSON> puede crear y gestionar listas de tareas para hacer seguimiento del progreso. Esto ayuda a organizar tareas complejas en pasos manejables."}}, "completion": {"description": "Configura la configuración de completado de código para mejorar tu experiencia de desarrollo.", "configureButton": "Configurar", "debounceTime": {"label": "Retraso de activación de completado", "description": "Tiempo de retraso para la activación del completado de código (milisegundos)"}, "number": {"label": "Número de completados", "description": "Número de candidatos de completado de código a generar"}, "granularity": {"label": "Preferencia de granularidad de completado", "description": "Configuración de preferencia de granularidad para el completado de código", "singleRow": "Línea <PERSON>", "oneTimeMaximization": "Maximización única", "balanced": "Equilibrado"}, "multipleLineCompletion": {"label": "Modo de completado multilínea", "description": "Modo de activación para el completado de código multilínea", "autoCompletion": "Completado automático", "triggerCompletion": "Completado activado"}, "maxTokens": {"label": "Tokens máximos", "description": "Número máximo de tokens para el completado de código"}}, "experimental": {"DIFF_STRATEGY_UNIFIED": {"name": "Usar estrategia de diff unificada experimental", "description": "Habilitar la estrategia de diff unificada experimental. Esta estrategia podría reducir el número de reintentos causados por errores del modelo, pero puede causar comportamientos inesperados o ediciones incorrectas. Habilítela solo si comprende los riesgos y está dispuesto a revisar cuidadosamente todos los cambios."}, "SEARCH_AND_REPLACE": {"name": "Usar herramienta experimental de búsqueda y reemplazo", "description": "Habilitar la herramienta experimental de búsqueda y reemplazo, permitiendo a Zhanlu reemplazar múltiples instancias de un término de búsqueda en una sola solicitud."}, "INSERT_BLOCK": {"name": "Usar herramienta experimental de inserción de contenido", "description": "Habilitar la herramienta experimental de inserción de contenido, permitiendo a Zhanlu insertar contenido en números de línea específicos sin necesidad de crear un diff."}, "POWER_STEERING": {"name": "Usar modo experimental de \"dirección asistida\"", "description": "<PERSON><PERSON>do está habilitado, <PERSON><PERSON><PERSON> al modelo los detalles de su definición de modo actual con más frecuencia. Esto llevará a una mayor adherencia a las definiciones de roles e instrucciones personalizadas, pero usará más tokens por mensaje."}, "MULTI_SEARCH_AND_REPLACE": {"name": "Usar herramienta experimental de diff de bloques múltiples", "description": "<PERSON>uando está habilitado, <PERSON><PERSON><PERSON> usará la herramienta de diff de bloques múltiples. Esto intentará actualizar múltiples bloques de código en el archivo en una sola solicitud."}, "CONCURRENT_FILE_READS": {"name": "Habilitar lectura concurrente de archivos", "description": "<PERSON>uando está habilitado, <PERSON><PERSON><PERSON> puede leer múltiples archivos en una sola solicitud. Cuando está deshabilitado, <PERSON><PERSON><PERSON> debe leer archivos uno a la vez. Deshabilitarlo puede ayudar cuando se trabaja con modelos menos capaces o cuando deseas más control sobre el acceso a archivos."}, "MARKETPLACE": {"name": "Habilitar Marketplace", "description": "Cuando está habilitado, puedes instalar MCP y modos personalizados del Marketplace."}, "MULTI_FILE_APPLY_DIFF": {"name": "Habilitar ediciones de archivos concurrentes", "description": "<PERSON>uando está habilitado, zhan<PERSON> puede editar múltiples archivos en una sola solicitud. Cuando está deshabilitado, zhan<PERSON> debe editar archivos de uno en uno. Deshabilitar esto puede ayudar cuando trabajas con modelos menos capaces o cuando quieres más control sobre las modificaciones de archivos."}}, "promptCaching": {"label": "Desactivar caché de prompts", "description": "<PERSON><PERSON>do está marcado, <PERSON><PERSON><PERSON> no utilizará el caché de prompts para este modelo."}, "temperature": {"useCustom": "Usar temperatura personalizada", "description": "Controla la aleatoriedad en las respuestas del modelo.", "rangeDescription": "Valores más altos hacen que la salida sea más aleatoria, valores más bajos la hacen más determinista."}, "modelInfo": {"supportsImages": "Soporta imágenes", "noImages": "No soporta imágenes", "supportsComputerUse": "Soporta uso del ordenador", "noComputerUse": "No soporta uso del ordenador", "supportsPromptCache": "Soporta caché de prompts", "noPromptCache": "No soporta caché de prompts", "maxOutput": "Salida máxima", "inputPrice": "Precio de entrada", "outputPrice": "<PERSON>cio de salida", "cacheReadsPrice": "Precio de lecturas de caché", "cacheWritesPrice": "Precio de escrituras de caché", "enableStreaming": "Habilitar streaming", "enableR1Format": "Habilitar parámetros del modelo R1", "enableR1FormatTips": "Debe habilitarse al utilizar modelos R1 como QWQ, para evitar el error 400", "useAzure": "Usar Azure", "azureApiVersion": "Establecer versión de API de Azure", "gemini": {"freeRequests": "* <PERSON><PERSON><PERSON> hasta {{count}} solicitudes por minuto. Después de eso, la facturación depende del tamaño del prompt.", "pricingDetails": "Para más información, consulte los detalles de precios.", "billingEstimate": "* La facturación es una estimación - el costo exacto depende del tamaño del prompt."}}, "modelPicker": {"automaticFetch": "La extensión obtiene automáticamente la lista más reciente de modelos disponibles en <serviceLink>{{serviceName}}</serviceLink>. Si no está seguro de qué modelo elegir, Zhanlu funciona mejor con <defaultModelLink>{{defaultModelId}}</defaultModelLink>. También puede buscar \"free\" para opciones sin costo actualmente disponibles.", "label": "<PERSON><PERSON>", "searchPlaceholder": "Buscar", "noMatchFound": "No se encontraron coincidencias", "useCustomModel": "Usar personalizado: {{modelId}}"}, "footer": {"feedback": "Si tiene alguna pregunta o comentario, no dude en <qqDocsLink>abrir un problema</qqDocsLink>.", "version": "<PERSON><PERSON><PERSON> v{{version}}", "telemetry": {"label": "Permit<PERSON> informes anónimos de errores y uso", "description": "<PERSON><PERSON><PERSON> a mejorar <PERSON> enviando datos de uso anónimos e informes de errores. Nunca se envía código, prompts o información personal. Consulte nuestra política de privacidad para más detalles."}, "settings": {"import": "Importar", "export": "Exportar", "reset": "Restablecer"}}, "thinkingBudget": {"maxTokens": "Tokens máximos", "maxThinkingTokens": "Tokens máximos de pensamiento"}, "validation": {"apiKey": "Debe proporcionar una clave API válida.", "awsRegion": "Debe elegir una región para usar con Amazon Bedrock.", "googleCloud": "Debe proporcionar un ID de proyecto y región de Google Cloud válidos.", "modelId": "Debe proporcionar un ID de modelo válido.", "modelSelector": "Debe proporcionar un selector de modelo válido.", "openAi": "Debe proporcionar una URL base, clave API y ID de modelo válidos.", "arn": {"invalidFormat": "Formato de ARN no válido. Por favor, verifique los requisitos de formato.", "regionMismatch": "Advertencia: La región en su ARN ({{arnRegion}}) no coincide con su región seleccionada ({{region}}). Esto puede causar problemas de acceso. El proveedor usará la región del ARN."}, "modelAvailability": "El ID de modelo ({{modelId}}) que proporcionó no está disponible. Por favor, elija un modelo diferente.", "providerNotAllowed": "El proveedor '{{provider}}' no está permitido por su organización", "modelNotAllowed": "El modelo '{{model}}' no está permitido para el proveedor '{{provider}}' por su organización", "profileInvalid": "Este perfil contiene un proveedor o modelo que no está permitido por su organización"}, "placeholders": {"apiKey": "Ingrese clave API...", "profileName": "Ingrese nombre del perfil", "accessKey": "Ingrese clave de acceso...", "secretKey": "Ingrese clave secreta...", "sessionToken": "Ingrese token de sesión...", "credentialsJson": "Ingrese JSON de credenciales...", "keyFilePath": "Ingrese ruta del archivo de clave...", "projectId": "Ingrese ID del proyecto...", "customArn": "Ingrese ARN (ej. arn:aws:bedrock:us-east-1:123456789012:foundation-model/my-model)", "baseUrl": "Ingrese URL base...", "modelId": {"lmStudio": "ej. meta-llama-3.1-8b-instruct", "lmStudioDraft": "ej. lmstudio-community/llama-3.2-1b-instruct", "ollama": "ej. llama3.1"}, "numbers": {"maxTokens": "ej. 4096", "contextWindow": "ej. 128000", "inputPrice": "ej. 0.0001", "outputPrice": "ej. 0.0002", "cacheWritePrice": "ej. 0.00005"}}, "defaults": {"ollamaUrl": "Predeterminado: http://localhost:11434", "lmStudioUrl": "Predeterminado: http://localhost:1234", "geminiUrl": "Predeterminado: https://generativelanguage.googleapis.com"}, "labels": {"customArn": "ARN personalizado", "useCustomArn": "Usar ARN personalizado..."}, "includeMaxOutputTokens": "Incluir tokens máximos de salida", "includeMaxOutputTokensDescription": "Enviar parámetro de tokens máximos de salida en solicitudes API. Algunos proveedores pueden no soportar esto.", "limitMaxTokensDescription": "Limitar el número máximo de tokens en la respuesta", "maxOutputTokensLabel": "Tokens máximos de salida", "maxTokensGenerateDescription": "Tokens máximos a generar en la respuesta"}