{"common": {"save": "<PERSON><PERSON>", "done": "<PERSON><PERSON>", "cancel": "<PERSON><PERSON><PERSON>", "reset": "R<PERSON><PERSON><PERSON>", "select": "Seleziona", "add": "Aggiungi intestazione", "remove": "<PERSON><PERSON><PERSON><PERSON>"}, "header": {"title": "Impostazioni", "saveButtonTooltip": "Salva modifiche", "nothingChangedTooltip": "Ness<PERSON> modifica", "doneButtonTooltip": "Scarta le modifiche non salvate e chiudi il pannello delle impostazioni"}, "unsavedChangesDialog": {"title": "Modifiche non salvate", "description": "Vuoi scartare le modifiche e continuare?", "cancelButton": "<PERSON><PERSON><PERSON>", "discardButton": "Scarta modifiche"}, "sections": {"providers": "Fornitori", "autoApprove": "Auto-approvazione", "browser": "Accesso computer", "checkpoints": "Punti di controllo", "notifications": "Notifiche", "contextManagement": "Contesto", "terminal": "Terminal", "completion": "Completamento", "prompts": "Prompt", "experimental": "Sperimentale", "language": "<PERSON><PERSON>", "about": "Informazioni su Zhanlu", "interface": "Interfaccia"}, "developerMode": {"title": "Modalità sviluppatore", "label": "Abilita modalità sviluppatore", "description": "La modalità sviluppatore fornisce funzionalità avanzate e opzioni di configurazione, incluse funzionalità sperimentali, impostazioni del terminale, gestione dei prompt e altro ancora."}, "prompts": {"description": "Configura i prompt di supporto utilizzati per azioni rapide come il miglioramento dei prompt, la spiegazione del codice e la risoluzione dei problemi. Questi prompt a<PERSON><PERSON> a fornire una migliore assistenza per le attività di sviluppo comuni."}, "codeIndex": {"title": "Indicizzazione del codice", "enableLabel": "Abilita indicizzazione del codice", "enableDescription": "Abilita l'indicizzazione del codice per una ricerca e una comprensione del contesto migliorate", "providerLabel": "Fornitore di embedding", "selectProviderPlaceholder": "Seleziona fornitore", "openaiProvider": "OpenAI", "ollamaProvider": "Ollama", "geminiProvider": "Gemini", "geminiApiKeyLabel": "Chiave API:", "geminiApiKeyPlaceholder": "Inserisci la tua chiave API Gemini", "mistralProvider": "<PERSON><PERSON><PERSON>", "mistralApiKeyLabel": "Chiave API:", "mistralApiKeyPlaceholder": "Inserisci la tua chiave API Mistral", "openaiCompatibleProvider": "Compatibile con OpenAI", "openAiKeyLabel": "Chiave API OpenAI", "openAiKeyPlaceholder": "Inserisci la tua chiave API OpenAI", "openAiCompatibleBaseUrlLabel": "URL di base", "openAiCompatibleApiKeyLabel": "Chiave API", "openAiCompatibleApiKeyPlaceholder": "Inserisci la tua chiave API", "openAiCompatibleModelDimensionLabel": "Dimensione Embedding:", "modelDimensionLabel": "Dimensione del modello", "openAiCompatibleModelDimensionPlaceholder": "es., 1536", "openAiCompatibleModelDimensionDescription": "La dimensione dell'embedding (dimensione di output) per il tuo modello. Controlla la documentazione del tuo provider per questo valore. Valori comuni: 384, 768, 1536, 3072.", "modelLabel": "<PERSON><PERSON>", "selectModelPlaceholder": "Seleziona modello", "ollamaUrlLabel": "URL Ollama:", "qdrantUrlLabel": "URL Qdrant", "qdrantKeyLabel": "<PERSON><PERSON> Qdrant:", "startIndexingButton": "Avvia", "clearIndexDataButton": "Cancella indice", "unsavedSettingsMessage": "Per favore salva le tue impostazioni prima di avviare il processo di indicizzazione.", "clearDataDialog": {"title": "Sei sicuro?", "description": "Questa azione non può essere annullata. Eliminerà permanentemente i dati di indice del tuo codice.", "cancelButton": "<PERSON><PERSON><PERSON>", "confirmButton": "<PERSON><PERSON>a dati"}, "description": "Configura le impostazioni di indicizzazione del codebase per abilitare la ricerca semantica del tuo progetto. <0>Scopri di più</0>", "statusTitle": "Stato", "settingsTitle": "Impostazioni di indicizzazione", "disabledMessage": "L'indicizzazione del codebase è attualmente disabilitata. Abilitala nelle impostazioni globali per configurare le opzioni di indicizzazione.", "embedderProviderLabel": "Provider <PERSON>", "modelPlaceholder": "Inserisci il nome del modello", "selectModel": "Seleziona un modello", "ollamaBaseUrlLabel": "URL base Ollama", "qdrantApiKeyLabel": "Chiave API Qdrant", "qdrantApiKeyPlaceholder": "Inserisci la tua chiave API Qdrant (opzionale)", "setupConfigLabel": "Impostazione", "ollamaUrlPlaceholder": "http://localhost:11434", "openAiCompatibleBaseUrlPlaceholder": "https://api.example.com", "modelDimensionPlaceholder": "1536", "qdrantUrlPlaceholder": "http://localhost:6333", "saveError": "Impossibile salvare le impostazioni", "modelDimensions": "({{dimension}} dimensioni)", "saveSuccess": "Impostazioni salvate con successo", "saving": "Salvataggio...", "saveSettings": "<PERSON><PERSON>", "indexingStatuses": {"standby": "In attesa", "indexing": "Indicizzazione", "indexed": "Indicizzato", "error": "Errore"}, "close": "<PERSON><PERSON>", "validation": {"invalidQdrantUrl": "URL Qdrant non valido", "invalidOllamaUrl": "URL Ollama non valido", "invalidBaseUrl": "URL di base non valido", "qdrantUrlRequired": "È richiesto l'URL di Qdrant", "openaiApiKeyRequired": "È richiesta la chiave API di OpenAI", "modelSelectionRequired": "È richiesta la selezione del modello", "apiKeyRequired": "È richiesta la chiave API", "modelIdRequired": "È richiesto l'ID del modello", "modelDimensionRequired": "È richiesta la dimensione del modello", "geminiApiKeyRequired": "È richiesta la chiave API Gemini", "mistralApiKeyRequired": "La chiave API di Mistral è richiesta", "ollamaBaseUrlRequired": "È richiesto l'URL di base di Ollama", "baseUrlRequired": "È richiesto l'URL di base", "modelDimensionMinValue": "La dimensione del modello deve essere maggiore di 0"}, "advancedConfigLabel": "Configurazione avanzata", "searchMinScoreLabel": "Soglia punteggio di ricerca", "searchMinScoreDescription": "Punteggio minimo di somiglianza (0.0-1.0) richiesto per i risultati della ricerca. Valori più bassi restituiscono più risultati ma potrebbero essere meno pertinenti. Valori più alti restituiscono meno risultati ma più pertinenti.", "searchMinScoreResetTooltip": "Ripristina al valore predefinito (0.4)", "searchMaxResultsLabel": "Risultati di ricerca massimi", "searchMaxResultsDescription": "Numero massimo di risultati di ricerca da restituire quando si interroga l'indice del codice. Valori più alti forniscono più contesto ma possono includere risultati meno pertinenti.", "resetToDefault": "Ripristina al valore predefinito"}, "autoApprove": {"description": "Permetti a zhanlu di eseguire automaticamente operazioni senza richiedere approvazione. Abilita queste impostazioni solo se ti fidi completamente dell'IA e comprendi i rischi di sicurezza associati.", "toggleAriaLabel": "Attiva/disattiva approvazione automatica", "disabledAriaLabel": "Approvazione automatica disabilitata - seleziona prima le opzioni", "readOnly": {"label": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON> a<PERSON>, <PERSON>hanlu visualizzerà automaticamente i contenuti della directory e leggerà i file senza richiedere di cliccare sul pulsante Approva.", "outsideWorkspace": {"label": "Includi file al di fuori dell'area di lavoro", "description": "Permetti a Zhanlu di leggere file al di fuori dell'area di lavoro attuale senza richiedere approvazione."}}, "write": {"label": "<PERSON><PERSON><PERSON>", "description": "Crea e modifica automaticamente i file senza richiedere approvazione", "delayLabel": "<PERSON><PERSON> dopo le scritture per consentire alla diagnostica di rilevare potenziali problemi", "outsideWorkspace": {"label": "Includi file al di fuori dell'area di lavoro", "description": "Permetti a Zhanlu di creare e modificare file al di fuori dell'area di lavoro attuale senza richiedere approvazione."}, "protected": {"label": "Includi file protetti", "description": "Permetti a Zhanlu di creare e modificare file protetti (come .zhanluignore e file di configurazione .zhanlu/) senza richiedere approvazione."}}, "browser": {"label": "Browser", "description": "Esegui automaticamente azioni del browser senza richiedere approvazione. Nota: Si applica solo quando il modello supporta l'uso del computer"}, "retry": {"label": "<PERSON><PERSON><PERSON><PERSON>", "description": "Riprova automaticamente le richieste API fallite quando il server restituisce una risposta di errore", "delayLabel": "Ritardo prima di riprovare la richiesta"}, "mcp": {"label": "MCP", "description": "Abilita l'approvazione automatica dei singoli strumenti MCP nella vista Server MCP (richiede sia questa impostazione che la casella \"Consenti sempre\" dello strumento)"}, "modeSwitch": {"label": "Modalità", "description": "Passa automaticamente tra diverse modalità senza richiedere approvazione"}, "subtasks": {"label": "Sottoattività", "description": "Consenti la creazione e il completamento di attività secondarie senza richiedere approvazione"}, "followupQuestions": {"label": "<PERSON><PERSON>", "description": "Seleziona automaticamente la prima risposta suggerita per le domande di follow-up dopo il timeout configurato", "timeoutLabel": "Tempo di attesa prima di selezionare automaticamente la prima risposta"}, "execute": {"label": "<PERSON><PERSON><PERSON><PERSON>", "description": "Esegui automaticamente i comandi del terminale consentiti senza richiedere approvazione", "allowedCommands": "Comandi di auto-esecuzione consentiti", "allowedCommandsDescription": "Prefissi di comando che possono essere auto-eseguiti quando \"Approva sempre operazioni di esecuzione\" è abilitato. Aggiungi * per consentire tutti i comandi (usare con cautela).", "deniedCommands": "<PERSON><PERSON><PERSON> negati", "deniedCommandsDescription": "Prefissi di comandi che verranno automaticamente negati senza richiedere approvazione. In caso di conflitti con comandi consentiti, la corrispondenza del prefisso più lungo ha la precedenza. Aggiungi * per negare tutti i comandi.", "commandPlaceholder": "<PERSON><PERSON><PERSON><PERSON> prefisso comando (es. 'git ')", "deniedCommandPlaceholder": "<PERSON><PERSON><PERSON><PERSON> prefisso comando da negare (es. 'rm -rf')", "addButton": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "autoDenied": "I comandi con il prefisso `{{prefix}}` sono stati vietati dall'utente. Non aggirare questa restrizione eseguendo un altro comando."}, "updateTodoList": {"label": "Todo", "description": "La lista delle cose da fare viene aggiornata automaticamente senza approvazione"}, "apiRequestLimit": {"title": "Richieste massime", "description": "Esegui automaticamente questo numero di richieste API prima di chiedere l'approvazione per continuare con l'attività.", "unlimited": "Illimitato"}, "selectOptionsFirst": "Seleziona almeno un'opzione qui sotto per abilitare l'approvazione automatica"}, "providers": {"providerDocumentation": "Documentazione {{provider}}", "configProfile": "Profilo di configurazione", "description": "Salva diverse configurazioni API per passare rapidamente tra fornitori e impostazioni.", "apiProvider": "Fornitore API", "model": "<PERSON><PERSON>", "nameEmpty": "Il nome non può essere vuoto", "nameExists": "Esiste già un profilo con questo nome", "nameTooLong": "Il nome non può superare 20 caratteri", "deleteProfile": "Elimina profilo", "invalidArnFormat": "Formato ARN non valido. Controlla gli esempi sopra.", "enterNewName": "Inserisci un nuovo nome", "addProfile": "Aggiungi profilo", "renameProfile": "Rinomina profilo", "newProfile": "Nuovo profilo di configurazione", "enterProfileName": "Inserisci il nome del profilo", "createProfile": "Crea profilo", "cannotDeleteOnlyProfile": "Impossibile eliminare l'unico profilo", "searchPlaceholder": "Cerca profili", "noMatchFound": "<PERSON><PERSON><PERSON> profilo corrispondente trovato", "searchProviderPlaceholder": "Cerca fornitori", "noProviderMatchFound": "<PERSON><PERSON><PERSON> forni<PERSON> trovato", "vscodeLmDescription": "L'API del Modello di Linguaggio di VS Code consente di eseguire modelli forniti da altre estensioni di VS Code (incluso, ma non limitato a, GitHub Copilot). Il modo più semplice per iniziare è installare le estensioni Copilot e Copilot Chat dal VS Code Marketplace.", "awsCustomArnUse": "Inserisci un ARN Amazon Bedrock valido per il modello che desideri utilizzare. Esempi di formato:", "awsCustomArnDesc": "Assicurati che la regione nell'ARN corrisponda alla regione AWS selezionata sopra.", "openRouterApiKey": "Chiave API OpenRouter", "getOpenRouterApiKey": "<PERSON><PERSON>eni chiave API OpenRouter", "apiKeyStorageNotice": "Le chiavi API sono memorizzate in modo sicuro nell'Archivio Segreto di VSCode", "glamaApiKey": "Chiave API Glama", "getGlamaApiKey": "Ottieni chiave API Glama", "useCustomBaseUrl": "Usa URL base personalizzato", "useReasoning": "Abilita ragionamento", "useHostHeader": "Usa intestazione Host personalizzata", "useLegacyFormat": "Usa formato API OpenAI legacy", "customHeaders": "Intestazioni personalizzate", "headerName": "Nome intestazione", "headerValue": "Valore intestazione", "noCustomHeaders": "Nessuna intestazione personalizzata definita. Fai clic sul pulsante + per aggiungerne una.", "requestyApiKey": "Chiave API Requesty", "refreshModels": {"label": "Aggiorna modelli", "hint": "Riapri le impostazioni per vedere i modelli più recenti.", "loading": "Aggiornamento dell'elenco dei modelli...", "success": "Elenco dei modelli aggiornato con successo!", "error": "Impossibile aggiornare l'elenco dei modelli. Riprova."}, "getRequestyApiKey": "Ottieni chiave API Requesty", "openRouterTransformsText": "Comprimi prompt e catene di messaggi alla dimensione del contesto (<a>Trasformazioni OpenRouter</a>)", "anthropicApiKey": "Chiave API Zhanlu", "getAnthropicApiKey": "<PERSON><PERSON>eni chiave <PERSON>", "anthropicUseAuthToken": "Passa la chiave API Anthropic come header di autorizzazione invece di X-Api-Key", "chutesApiKey": "Chiave API Chutes", "getChutesApiKey": "Ottieni chiave API Chutes", "deepSeekApiKey": "Chiave API DeepSeek", "getDeepSeekApiKey": "O<PERSON>eni chiave API DeepSeek", "moonshotApiKey": "Chiave API Moonshot", "getMoonshotApiKey": "<PERSON><PERSON>eni chiave API Moonshot", "moonshotBaseUrl": "Punto di ingresso Moonshot", "geminiApiKey": "Chiave API Gemini", "getGroqApiKey": "Ottieni chiave API Groq", "groqApiKey": "Chiave API Groq", "getHuggingFaceApiKey": "Ottieni chiave API Hugging Face", "huggingFaceApiKey": "Chiave API Hugging Face", "huggingFaceModelId": "ID modello", "huggingFaceLoading": "Caricamento...", "huggingFaceModelsCount": "({{count}} modelli)", "huggingFaceSelectModel": "Seleziona un modello...", "huggingFaceSearchModels": "Cerca modelli...", "huggingFaceNoModelsFound": "<PERSON><PERSON><PERSON> trovato", "huggingFaceProvider": "Provider", "huggingFaceProviderAuto": "Automatico", "huggingFaceSelectProvider": "Seleziona un provider...", "huggingFaceSearchProviders": "Cerca provider...", "huggingFaceNoProvidersFound": "Nessun provider trovato", "getGeminiApiKey": "Ottieni chiave API Gemini", "openAiApiKey": "Chiave API OpenAI", "apiKey": "Chiave API", "openAiBaseUrl": "URL base", "getOpenAiApiKey": "Ottieni chiave API OpenAI", "mistralApiKey": "Chiave API Mistral", "getMistralApiKey": "Ottieni chiave API Mistral / Codestral", "codestralBaseUrl": "URL base Codestral (opzionale)", "codestralBaseUrlDesc": "Imposta un URL opzionale per i modelli Codestral.", "xaiApiKey": "Chiave API xAI", "getXaiApiKey": "Ottieni chiave API xAI", "litellmApiKey": "Chiave API LiteLLM", "litellmBaseUrl": "URL base LiteLLM", "awsCredentials": "Credenziali AWS", "awsProfile": "Profilo AWS", "awsApiKey": "Chiave API Amazon Bedrock", "awsProfileName": "Nome profilo AWS", "awsAccessKey": "Chiave di accesso AWS", "awsSecretKey": "Chiave segreta AWS", "awsSessionToken": "Token di sessione AWS", "awsRegion": "Regione AWS", "awsCrossRegion": "Usa inferenza cross-regione", "awsBedrockVpc": {"useCustomVpcEndpoint": "Usa endpoint VPC personalizzato", "vpcEndpointUrlPlaceholder": "Inserisci URL endpoint VPC (opzionale)", "examples": "Esempi:"}, "enablePromptCaching": "Abilita cache dei prompt", "enablePromptCachingTitle": "Abilita la cache dei prompt per migliorare le prestazioni e ridurre i costi per i modelli supportati.", "cacheUsageNote": "Nota: Se non vedi l'utilizzo della cache, prova a selezionare un modello diverso e poi seleziona nuovamente il modello desiderato.", "vscodeLmModel": "Modello linguistico", "vscodeLmWarning": "Nota: Questa è un'integrazione molto sperimentale e il supporto del fornitore varierà. Se ricevi un errore relativo a un modello non supportato, si tratta di un problema del fornitore.", "googleCloudSetup": {"title": "Per utilizzare Google Cloud Vertex AI, è necessario:", "step1": "1. Creare un account Google Cloud, abilitare l'API Vertex AI e abilitare i modelli Zhanlu desiderati.", "step2": "2. Installare Google Cloud CLI e configurare le credenziali predefinite dell'applicazione.", "step3": "3. Oppure creare un account di servizio con credenziali."}, "googleCloudCredentials": "Credenziali Google Cloud", "googleCloudKeyFile": "Percorso file chiave Google Cloud", "googleCloudProjectId": "ID progetto Google Cloud", "googleCloudRegion": "Regione Google Cloud", "lmStudio": {"baseUrl": "URL base (opzionale)", "modelId": "ID modello", "speculativeDecoding": "Abilita decodifica speculativa", "draftModelId": "ID modello bozza", "draftModelDesc": "Per un corretto funzionamento della decodifica speculativa, il modello bozza deve provenire dalla stessa famiglia di modelli.", "selectDraftModel": "Seleziona modello b<PERSON>za", "noModelsFound": "Nessun modello bozza trovato. Assicurati che LM Studio sia in esecuzione con la modalità server abilitata.", "description": "LM Studio ti permette di eseguire modelli localmente sul tuo computer. Per iniziare, consulta la loro <a>guida rapida</a>. Dovrai anche avviare la funzionalità <b>server locale</b> di LM Studio per utilizzarlo con questa estensione. <span>Nota:</span> Zhanlu utilizza prompt complessi e funziona meglio con i modelli Zhanlu. I modelli con capacità inferiori potrebbero non funzionare come previsto."}, "ollama": {"baseUrl": "URL base (opzionale)", "modelId": "ID modello", "description": "Ollama ti permette di eseguire modelli localmente sul tuo computer. Per iniziare, consulta la guida rapida.", "warning": "Nota: <PERSON><PERSON><PERSON> utilizza prompt complessi e funziona meglio con i modelli <PERSON>lu. I modelli con capacità inferiori potrebbero non funzionare come previsto."}, "unboundApiKey": "Chiave API Unbound", "getUnboundApiKey": "Ottieni chiave API Unbound", "unboundRefreshModelsSuccess": "Lista dei modelli aggiornata! Ora puoi selezionare tra gli ultimi modelli.", "unboundInvalidApiKey": "Chiave API non valida. Controlla la tua chiave API e riprova.", "humanRelay": {"description": "Non è richiesta alcuna chiave API, ma l'utente dovrà aiutare a copiare e incollare le informazioni nella chat web AI.", "instructions": "Durant<PERSON> l'uso, apparirà una finestra di dialogo e il messaggio corrente verrà automaticamente copiato negli appunti. Dovrai incollarlo nelle versioni web dell'AI (come Zhanlu), quindi copiare la risposta dell'AI nella finestra di dialogo e fare clic sul pulsante di conferma."}, "openRouter": {"providerRouting": {"title": "Routing dei fornitori OpenRouter", "description": "OpenRouter indirizza le richieste ai migliori fornitori disponibili per il tuo modello. Per impostazione predefinita, le richieste sono bilanciate tra i principali fornitori per massimizzare il tempo di attività. Tuttavia, puoi scegliere un fornitore specifico da utilizzare per questo modello.", "learnMore": "Scopri di più sul routing dei fornitori"}}, "customModel": {"capabilities": "Configura le capacità e i prezzi del tuo modello personalizzato compatibile con OpenAI. Fai attenzione quando specifichi le capacità del modello, poiché possono influenzare le prestazioni di Zhanlu.", "maxTokens": {"label": "Token di output massimi", "description": "Numero massimo di token che il modello può generare in una risposta. (Specifica -1 per lasciare che il server imposti il massimo token.)"}, "contextWindow": {"label": "Dimensione finestra di contesto", "description": "Numero totale di token (input + output) che il modello può elaborare."}, "imageSupport": {"label": "<PERSON><PERSON> immagini", "description": "Il modello è in grado di elaborare e comprendere le immagini?"}, "computerUse": {"label": "Uso del computer", "description": "Il modello è in grado di interagire con il browser? (es<PERSON> <PERSON><PERSON><PERSON>)."}, "promptCache": {"label": "<PERSON>ache dei prompt", "description": "Il modello è in grado di memorizzare in cache i prompt?"}, "pricing": {"input": {"label": "Prezzo input", "description": "Costo per milione di token di input/prompt. Questo influisce sul costo di invio di contesto e istruzioni al modello."}, "output": {"label": "Prezzo output", "description": "Costo per milione di token della risposta del modello. Questo influisce sul costo del contenuto generato e dei completamenti."}, "cacheReads": {"label": "Prezzo letture cache", "description": "Costo per milione di token per leggere dalla cache. Questo prezzo viene applicato quando si riceve una risposta memorizzata nella cache."}, "cacheWrites": {"label": "Prezzo scritture cache", "description": "Costo per milione di token per scrivere nella cache. Questo prezzo viene applicato quando si memorizza un prompt nella cache per la prima volta."}}, "resetDefaults": "Ripristina valori predefiniti"}, "rateLimitSeconds": {"label": "Limite di frequenza", "description": "Tempo minimo tra le richieste API."}, "consecutiveMistakeLimit": {"label": "Limite di errori e ripetizioni", "description": "Numero di errori consecutivi o azioni ripetute prima di mostrare la finestra di dialogo 'Roo sta riscontrando problemi'", "unlimitedDescription": "Tentativi illimitati abilitati (procedi automaticamente). La finestra di dialogo non verrà mai visualizzata.", "warning": "⚠️ L'impostazione a 0 consente tentativi illimitati che possono consumare un notevole utilizzo dell'API"}, "reasoningEffort": {"label": "Sforzo di ragionamento del modello", "high": "Alto", "medium": "Medio", "low": "<PERSON><PERSON>"}, "setReasoningLevel": "Abilita sforzo di ragionamento", "claudeCode": {"pathLabel": "Percorso Claude Code", "description": "Percorso facoltativo per la tua CLI Claude Code. Predefinito 'claude' se non impostato.", "placeholder": "Predefinito: claude", "maxTokensLabel": "Token di output massimi", "maxTokensDescription": "Numero massimo di token di output per le risposte di Claude Code. Il valore predefinito è 8000."}}, "browser": {"enable": {"label": "Abilita strumento browser", "description": "<PERSON>uan<PERSON> a<PERSON>, <PERSON><PERSON><PERSON> pu<PERSON> utilizzare un browser per interagire con siti web quando si utilizzano modelli che supportano l'uso del computer."}, "viewport": {"label": "Dimensione viewport", "description": "Seleziona la dimensione del viewport per le interazioni del browser. Questo influisce su come i siti web vengono visualizzati e su come vi si interagisce.", "options": {"largeDesktop": "Desktop grande (1280x800)", "smallDesktop": "Desktop piccolo (900x600)", "tablet": "Tablet (768x1024)", "mobile": "Mobile (360x640)"}}, "screenshotQuality": {"label": "Qualità screenshot", "description": "Regola la qualità WebP degli screenshot del browser. Valori più alti forniscono screenshot più nitidi ma aumentano l'utilizzo di token."}, "remote": {"label": "Usa connessione browser remoto", "description": "Connettiti a un browser Chrome in esecuzione con debug remoto abilitato (--remote-debugging-port=9222).", "urlPlaceholder": "URL personalizzato (es. http://localhost:9222)", "testButton": "Testa connessione", "testingButton": "Test in corso...", "instructions": "Inserisci l'indirizzo host del protocollo DevTools o lascia vuoto per scoprire automaticamente le istanze Chrome locali. Il pulsante Test Connessione proverà l'URL personalizzato se fornito, o scoprirà automaticamente se il campo è vuoto."}}, "checkpoints": {"enable": {"label": "Abilita punti di controllo automatici", "description": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> c<PERSON>à automaticamente punti di controllo durante l'esecuzione dei compiti, facilitando la revisione delle modifiche o il ritorno a stati precedenti."}}, "notifications": {"sound": {"label": "Abilita effetti sonori", "description": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>urrà effetti sonori per notifiche ed eventi.", "volumeLabel": "Volume"}, "tts": {"label": "Abilita sintesi vocale", "description": "<PERSON><PERSON><PERSON> a<PERSON>, <PERSON><PERSON><PERSON> ad alta voce le sue risposte utilizzando la sintesi vocale.", "speedLabel": "Velocità"}}, "contextManagement": {"description": "Controlla quali informazioni sono incluse nella finestra di contesto dell'IA, influenzando l'utilizzo di token e la qualità delle risposte", "autoCondenseContextPercent": {"label": "Soglia per attivare la condensazione intelligente del contesto", "description": "Quando la finestra di contesto raggiunge questa soglia, z<PERSON><PERSON> la condenserà automaticamente."}, "condensingApiConfiguration": {"label": "Configurazione API per la condensazione del contesto", "description": "Seleziona quale configurazione API utilizzare per le operazioni di condensazione del contesto. Lascia deselezionato per utilizzare la configurazione attiva corrente.", "useCurrentConfig": "Predefinito"}, "customCondensingPrompt": {"label": "Prompt personalizzato condensazione contesto", "description": "Prompt di sistema personalizzato per la condensazione del contesto. <PERSON>cia vuoto per utilizzare il prompt predefinito.", "placeholder": "Inserisci qui il tuo prompt di condensazione personalizzato...\n\nPuoi utilizzare la stessa struttura del prompt predefinito:\n- Conversazione precedente\n- Lavoro attuale\n- Concetti tecnici chiave\n- File e codice pertinenti\n- Risoluzione dei problemi\n- Attività in sospeso e prossimi passi", "reset": "<PERSON><PERSON><PERSON><PERSON> predefinito", "hint": "Vuoto = usa prompt predefinito"}, "autoCondenseContext": {"name": "Attiva automaticamente la condensazione intelligente del contesto", "description": "Quando abilitato, <PERSON><PERSON><PERSON> condenserà automaticamente il contesto quando viene raggiunta la soglia. Quando disabilitato, puoi ancora attivare manualmente la condensazione del contesto."}, "openTabs": {"label": "Limite contesto schede aperte", "description": "Numero massimo di schede VSCode aperte da includere nel contesto. Valori più alti forniscono più contesto ma aumentano l'utilizzo di token."}, "workspaceFiles": {"label": "Limite contesto file area di lavoro", "description": "Numero massimo di file da includere nei dettagli della directory di lavoro corrente. Valori più alti forniscono più contesto ma aumentano l'utilizzo di token."}, "rooignore": {"label": "Mostra file .zhanluignore negli elenchi e nelle ricerche", "description": "Quando abilitato, i file che corrispondono ai pattern in .zhanluignore verranno mostrati negli elenchi con un simbolo di blocco. Quando disabilitato, questi file saranno completamente nascosti dagli elenchi di file e dalle ricerche."}, "maxReadFile": {"label": "Soglia di auto-troncamento lettura file", "description": "Zhanlu legge questo numero di righe quando il modello omette i valori di inizio/fine. Se questo numero è inferiore al totale del file, Zhanlu genera un indice dei numeri di riga delle definizioni di codice. Casi speciali: -1 indica a Zhanlu di leggere l'intero file (senza indicizzazione), e 0 indica di non leggere righe e fornire solo indici di riga per un contesto minimo. Valori più bassi minimizzano l'utilizzo iniziale del contesto, permettendo successive letture precise di intervalli di righe. Le richieste con inizio/fine espliciti non sono limitate da questa impostazione.", "lines": "righe", "always_full_read": "Leggi sempre l'intero file"}, "maxConcurrentFileReads": {"label": "Limite letture simultanee", "description": "Numero massimo di file che lo strumento 'read_file' può elaborare contemporaneamente. Valori più alti possono velocizzare la lettura di più file piccoli ma aumentano l'utilizzo della memoria."}, "diagnostics": {"includeMessages": {"label": "Includi automaticamente la diagnostica nel contesto", "description": "<PERSON>uando abilitato, i messaggi diagnostici (errori) dai file modificati verranno automaticamente inclusi nel contesto. Puoi sempre includere manualmente tutta la diagnostica del workspace usando @problems."}, "maxMessages": {"label": "Numero massimo di messaggi diagnostici", "description": "Numero massimo di messaggi diagnostici da includere per file. Questo limite si applica sia all'inclusione automatica (quando la casella è abilitata) che alle menzioni manuali di @problems. Valori più alti forniscono più contesto ma aumentano l'utilizzo dei token.", "resetTooltip": "Ripristina al valore predefinito (50)", "unlimited": "Messaggi diagnostici illimitati", "unlimitedLabel": "Illimitato"}, "delayAfterWrite": {"label": "<PERSON><PERSON> dopo le scritture per consentire alla diagnostica di rilevare potenziali problemi", "description": "Tempo di attesa dopo la scrittura dei file prima di procedere, consentendo agli strumenti di diagnostica di elaborare le modifiche e rilevare i problemi."}}, "condensingThreshold": {"label": "Soglia di attivazione condensazione", "selectProfile": "Configura soglia per profilo", "defaultProfile": "Predefinito globale (tutti i profili)", "defaultDescription": "Quando il contesto raggiunge questa percentuale, verrà automaticamente condensato per tutti i profili a meno che non abbiano impostazioni personalizzate", "profileDescription": "Soglia personalizzata solo per questo profilo (sovrascrive il predefinito globale)", "inheritDescription": "Questo profilo eredita la soglia predefinita globale ({{threshold}}%)", "usesGlobal": "(usa globale {{threshold}}%)"}}, "terminal": {"basic": {"label": "Impostazioni terminale: Base", "description": "Impostazioni base del terminale"}, "advanced": {"label": "Impostazioni terminale: Avanzate", "description": "Le seguenti opzioni potrebbero richiedere il riavvio del terminale per applicare l'impostazione."}, "outputLineLimit": {"label": "Limite output terminale", "description": "Numero massimo di righe da includere nell'output del terminale durante l'esecuzione dei comandi. <PERSON>uando superato, le righe verranno rimosse dal centro, rispar<PERSON><PERSON> token. <0>Scopri di più</0>"}, "outputCharacterLimit": {"label": "Limite di caratteri del terminale", "description": "Numero massimo di caratteri da includere nell'output del terminale durante l'esecuzione dei comandi. Questo limite ha la precedenza sul limite di righe per prevenire problemi di memoria causati da righe estremamente lunghe. Se superato, l'output verrà troncato. <0>Scopri di più</0>"}, "shellIntegrationTimeout": {"label": "Timeout integrazione shell del terminale", "description": "Tempo massimo di attesa per l'inizializzazione dell'integrazione della shell prima di eseguire i comandi. Per gli utenti con tempi di avvio della shell lunghi, questo valore potrebbe dover essere aumentato se si vedono errori \"Shell Integration Unavailable\" nel terminale. <0>Scopri di più</0>"}, "shellIntegrationDisabled": {"label": "Disabilita l'integrazione della shell del terminale", "description": "Abilita questa opzione se i comandi del terminale non funzionano correttamente o se vedi errori 'Shell Integration Unavailable'. Questo utilizza un metodo più semplice per eseguire i comandi, bypassando alcune funzionalità avanzate del terminale. <0>Scopri di più</0>"}, "commandDelay": {"label": "Ritardo comando terminale", "description": "Ritardo in millisecondi da aggiungere dopo l'esecuzione del comando. L'impostazione predefinita di 0 disabilita completamente il ritardo. Questo può aiutare a garantire che l'output del comando sia catturato completamente nei terminali con problemi di temporizzazione. Nella maggior parte dei terminali viene implementato impostando `PROMPT_COMMAND='sleep N'` e Powershell aggiunge `start-sleep` alla fine di ogni comando. In origine era una soluzione per il bug VSCode#237208 e potrebbe non essere necessario. <0>Scopri di più</0>"}, "compressProgressBar": {"label": "Comprimi output barre di progresso", "description": "<PERSON>uando abilitato, elabora l'output del terminale con ritorni a capo (\\r) per simulare come un terminale reale visualizzerebbe il contenuto. Questo rimuove gli stati intermedi delle barre di progresso, mantenendo solo lo stato finale, il che conserva spazio di contesto per informazioni più rilevanti. <0>Scopri di più</0>"}, "powershellCounter": {"label": "Abilita soluzione temporanea contatore PowerShell", "description": "<PERSON>uando abilitato, aggiunge un contatore ai comandi PowerShell per garantire la corretta esecuzione dei comandi. Questo aiuta con i terminali PowerShell che potrebbero avere problemi con la cattura dell'output. <0>Scopri di più</0>"}, "zshClearEolMark": {"label": "Cancella marcatore fine riga ZSH", "description": "Quando abilitato, cancella il marcatore di fine riga ZSH impostando PROMPT_EOL_MARK=''. Questo previene problemi con l'interpretazione dell'output dei comandi quando termina con caratteri speciali come '%'. <0>Scopri di più</0>"}, "zshOhMy": {"label": "Abilita integrazione Oh My Zsh", "description": "Quando abilitato, imposta ITERM_SHELL_INTEGRATION_INSTALLED=Yes per abilitare le funzionalità di integrazione della shell Oh My Zsh. L'applicazione di questa impostazione potrebbe richiedere il riavvio dell'IDE. <0>Scopri di più</0>"}, "zshP10k": {"label": "Abilita integrazione Powerlevel10k", "description": "<PERSON>uando abilita<PERSON>, imposta POWERLEVEL9K_TERM_SHELL_INTEGRATION=true per abilitare le funzionalità di integrazione della shell Powerlevel10k. <0>Scopri di più</0>"}, "zdotdir": {"label": "Abilita gestione ZDOTDIR", "description": "<PERSON>uando abilitato, crea una directory temporanea per ZDOTDIR per gestire correttamente l'integrazione della shell zsh. Questo assicura che l'integrazione della shell VSCode funzioni correttamente con zsh mantenendo la tua configurazione zsh. <0>Scopri di più</0>"}, "inheritEnv": {"label": "Eredita variabili d'ambiente", "description": "<PERSON>uando abilita<PERSON>, il terminale eredita le variabili d'ambiente dal processo padre di VSCode, come le impostazioni di integrazione della shell definite nel profilo utente. Questo attiva direttamente l'impostazione globale di VSCode `terminal.integrated.inheritEnv`. <0>Scopri di più</0>"}}, "advancedSettings": {"title": "Impostazioni avanzate"}, "advanced": {"diff": {"label": "Abilita modifica tramite diff", "description": "<PERSON><PERSON><PERSON> a<PERSON>, <PERSON><PERSON><PERSON> in grado di modificare i file più velocemente e rifiuterà automaticamente scritture di file completi troncati. Funziona meglio con l'ultimo modello <PERSON>.", "strategy": {"label": "Strategia diff", "options": {"standard": "Standard (<PERSON><PERSON>)", "multiBlock": "Sperimentale: Diff multi-blocco", "unified": "Sperimentale: Diff unificato"}, "descriptions": {"standard": "La strategia diff standard applica modifiche a un singolo blocco di codice alla volta.", "unified": "La strategia diff unificato adotta diversi approcci per applicare i diff e sceglie il migliore.", "multiBlock": "La strategia diff multi-blocco consente di aggiornare più blocchi di codice in un file in una singola richiesta."}}, "matchPrecision": {"label": "<PERSON><PERSON><PERSON> corrispond<PERSON>", "description": "Questo cursore controlla quanto precisamente le sezioni di codice devono corrispondere quando si applicano i diff. Valori più bassi consentono corrispondenze più flessibili ma aumentano il rischio di sostituzioni errate. Usa valori inferiori al 100% con estrema cautela."}}, "todoList": {"label": "Abilita strumento lista di cose da fare", "description": "<PERSON>uando abilita<PERSON>, z<PERSON><PERSON> può creare e gestire liste di cose da fare per tracciare il progresso delle attività. Questo aiuta a organizzare attività complesse in passaggi gestibili."}}, "completion": {"description": "Configura le impostazioni di completamento del codice per migliorare la tua esperienza di sviluppo.", "configureButton": "Configura", "debounceTime": {"label": "Ritardo di attivazione completamento", "description": "Tempo di ritardo per l'attivazione del completamento del codice (millisecondi)"}, "number": {"label": "Numero di completamenti", "description": "Numero di candidati di completamento del codice da generare"}, "granularity": {"label": "Preferenza granularità completamento", "description": "Impostazione di preferenza della granularità per il completamento del codice", "singleRow": "Riga singola", "oneTimeMaximization": "Massimizzazione unica", "balanced": "Bilanciato"}, "multipleLineCompletion": {"label": "Modalità completamento multiriga", "description": "Modalità di attivazione per il completamento del codice multiriga", "autoCompletion": "Completamento automatico", "triggerCompletion": "Completamento attivato"}, "maxTokens": {"label": "<PERSON><PERSON> massimi", "description": "Numero massimo di token per il completamento del codice"}}, "experimental": {"DIFF_STRATEGY_UNIFIED": {"name": "Usa strategia diff unificata sperimentale", "description": "Abilita la strategia diff unificata sperimentale. Questa strategia potrebbe ridurre il numero di tentativi causati da errori del modello, ma può causare comportamenti imprevisti o modifiche errate. Abilitala solo se comprendi i rischi e sei disposto a rivedere attentamente tutte le modifiche."}, "SEARCH_AND_REPLACE": {"name": "Usa strumento di ricerca e sostituzione sperimentale", "description": "Abilita lo strumento di ricerca e sostituzione sperimentale, consentendo a Zhanlu di sostituire più istanze di un termine di ricerca in una singola richiesta."}, "INSERT_BLOCK": {"name": "Usa strumento di inserimento contenuti sperimentale", "description": "Abilita lo strumento di inserimento contenuti sperimentale, consentendo a Zhanlu di inserire contenuti a numeri di riga specifici senza dover creare un diff."}, "POWER_STEERING": {"name": "Usa modalità \"servosterzo\" sperimentale", "description": "<PERSON><PERSON><PERSON> a<PERSON>, <PERSON><PERSON><PERSON> rico<PERSON> al modello i dettagli della sua definizione di modalità corrente più frequentemente. <PERSON>o porterà a una maggiore aderenza alle definizioni dei ruoli e alle istruzioni personalizzate, ma utilizzerà più token per messaggio."}, "MULTI_SEARCH_AND_REPLACE": {"name": "Usa strumento diff multi-blocco sperimentale", "description": "<PERSON><PERSON><PERSON> a<PERSON>, <PERSON><PERSON><PERSON> utiliz<PERSON> lo strumento diff multi-blocco. Questo tenterà di aggiornare più blocchi di codice nel file in una singola richiesta."}, "CONCURRENT_FILE_READS": {"name": "Abilita lettura simultanea dei file", "description": "<PERSON>uando abilitato, z<PERSON><PERSON> può leggere più file in una singola richiesta. Quando disabilitato, zhan<PERSON> deve leggere i file uno alla volta. Disabilitarlo può aiutare quando si lavora con modelli meno capaci o quando si desidera maggiore controllo sull'accesso ai file."}, "MARKETPLACE": {"name": "Abilita Marketplace", "description": "<PERSON><PERSON><PERSON> a<PERSON>, puoi installare MCP e modalità personalizzate dal Marketplace."}, "MULTI_FILE_APPLY_DIFF": {"name": "Abilita modifiche di file concorrenti", "description": "<PERSON>uando abilitato, z<PERSON><PERSON> può modificare più file in una singola richiesta. Quando disabilitato, zhan<PERSON> deve modificare i file uno alla volta. Disabilitare questa opzione può aiutare quando lavori con modelli meno capaci o quando vuoi più controllo sulle modifiche dei file."}}, "promptCaching": {"label": "Disattiva la cache dei prompt", "description": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> non utilizzerà la cache dei prompt per questo modello."}, "temperature": {"useCustom": "Usa temperatura personalizzata", "description": "Controlla la casualità nelle risposte del modello.", "rangeDescription": "Valori più alti rendono l'output più casuale, valori più bassi lo rendono più deterministico."}, "modelInfo": {"supportsImages": "<PERSON>a immagini", "noImages": "Non supporta immagini", "supportsComputerUse": "Supporta uso del computer", "noComputerUse": "Non supporta uso del computer", "supportsPromptCache": "Supporta cache dei prompt", "noPromptCache": "Non supporta cache dei prompt", "maxOutput": "Output massimo", "inputPrice": "Prezzo input", "outputPrice": "Prezzo output", "cacheReadsPrice": "Prezzo letture cache", "cacheWritesPrice": "Prezzo scritture cache", "enableStreaming": "Abilita streaming", "enableR1Format": "Abilita i parametri del modello R1", "enableR1FormatTips": "Deve essere abilitato quando si utilizzano modelli R1 come QWQ, per evitare l'errore 400", "useAzure": "Usa Azure", "azureApiVersion": "Imposta versione API Azure", "gemini": {"freeRequests": "* Gratuito fino a {{count}} richieste al minuto. Dopo, la fatturazione dipende dalla dimensione del prompt.", "pricingDetails": "Per maggiori informazioni, vedi i dettagli sui prezzi.", "billingEstimate": "* La fatturazione è una stima - il costo esatto dipende dalle dimensioni del prompt."}}, "modelPicker": {"automaticFetch": "L'estensione recupera automaticamente l'elenco più recente dei modelli disponibili su <serviceLink>{{serviceName}}</serviceLink>. Se non sei sicuro di quale modello scegliere, Zhanlu funziona meglio con <defaultModelLink>{{defaultModelId}}</defaultModelLink>. Puoi anche cercare \"free\" per opzioni gratuite attualmente disponibili.", "label": "<PERSON><PERSON>", "searchPlaceholder": "Cerca", "noMatchFound": "Nessuna corrispondenza trovata", "useCustomModel": "<PERSON>a personalizzato: {{modelId}}"}, "footer": {"feedback": "Se hai domande o feedback, non esitare a <qqDocsLink>segnalare un problema</qqDocsLink>.", "version": "<PERSON><PERSON><PERSON> v{{version}}", "telemetry": {"label": "Consenti segnalazioni anonime di errori e utilizzo", "description": "Aiuta a migliorare Zhanlu inviando dati di utilizzo anonimi e segnalazioni di errori. Non vengono mai inviati codice, prompt o informazioni personali. Consulta la nostra politica sulla privacy per maggiori dettagli."}, "settings": {"import": "Importa", "export": "Esporta", "reset": "R<PERSON><PERSON><PERSON>"}}, "thinkingBudget": {"maxTokens": "<PERSON><PERSON> massimi", "maxThinkingTokens": "Token massimi di pensiero"}, "validation": {"apiKey": "È necessario fornire una chiave API valida.", "awsRegion": "È necessario scegliere una regione per utilizzare Amazon Bedrock.", "googleCloud": "È necessario fornire un ID progetto e una regione Google Cloud validi.", "modelId": "È necessario fornire un ID modello valido.", "modelSelector": "È necessario fornire un selettore di modello valido.", "openAi": "È necessario fornire un URL base, una chiave API e un ID modello validi.", "arn": {"invalidFormat": "Formato ARN non valido. Verificare i requisiti del formato.", "regionMismatch": "Attenzione: La regione nel tuo ARN ({{arnRegion}}) non corrisponde alla regione selezionata ({{region}}). Questo potrebbe causare problemi di accesso. Il provider utilizzerà la regione dall'ARN."}, "modelAvailability": "L'ID modello ({{modelId}}) fornito non è disponibile. Seleziona un modello diverso.", "providerNotAllowed": "Il fornitore '{{provider}}' non è consentito dalla tua organizzazione", "modelNotAllowed": "Il modello '{{model}}' non è consentito per il fornitore '{{provider}}' dalla tua organizzazione.", "profileInvalid": "Questo profilo contiene un fornitore o un modello non consentito dalla tua organizzazione."}, "placeholders": {"apiKey": "Inserisci chiave API...", "profileName": "Inserisci nome profilo", "accessKey": "Inserisci chiave di accesso...", "secretKey": "Inserisci chiave segreta...", "sessionToken": "Inserisci token di sessione...", "credentialsJson": "Inserisci JSON delle credenziali...", "keyFilePath": "Inserisci percorso file chiave...", "projectId": "Inserisci ID progetto...", "customArn": "Inserisci ARN (es. arn:aws:bedrock:us-east-1:123456789012:foundation-model/my-model)", "baseUrl": "Inserisci URL base...", "modelId": {"lmStudio": "es. meta-llama-3.1-8b-instruct", "lmStudioDraft": "es. lmstudio-community/llama-3.2-1b-instruct", "ollama": "es. llama3.1"}, "numbers": {"maxTokens": "es. 4096", "contextWindow": "es. 128000", "inputPrice": "es. 0.0001", "outputPrice": "es. 0.0002", "cacheWritePrice": "es. 0.00005"}}, "defaults": {"ollamaUrl": "Predefinito: http://localhost:11434", "lmStudioUrl": "Predefinito: http://localhost:1234", "geminiUrl": "Predefinito: https://generativelanguage.googleapis.com"}, "labels": {"customArn": "ARN personalizzato", "useCustomArn": "Usa ARN personalizzato..."}, "includeMaxOutputTokens": "Includi token di output massimi", "includeMaxOutputTokensDescription": "Invia il parametro dei token di output massimi nelle richieste API. Alcuni provider potrebbero non supportarlo.", "limitMaxTokensDescription": "Limita il numero massimo di token nella risposta", "maxOutputTokensLabel": "Token di output massimi", "maxTokensGenerateDescription": "Token massimi da generare nella risposta"}