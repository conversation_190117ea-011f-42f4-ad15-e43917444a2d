{"common": {"save": "<PERSON><PERSON>", "done": "Tamamlandı", "cancel": "İptal", "reset": "Sıfırla", "select": "Seç", "add": "Başlık Ekle", "remove": "Kaldır"}, "header": {"title": "<PERSON><PERSON><PERSON>", "saveButtonTooltip": "Değişiklikleri kaydet", "nothingChangedTooltip": "Hiçbir şey değişmedi", "doneButtonTooltip": "Kaydedilmemiş değişiklikleri at ve ayarlar panelini kapat"}, "unsavedChangesDialog": {"title": "Kay<PERSON>il<PERSON><PERSON><PERSON>şiklikler", "description": "Değişiklikleri atmak ve devam etmek istiyor musunuz?", "cancelButton": "İptal", "discardButton": "Değişiklikleri At"}, "sections": {"providers": "Sağlayıcılar", "autoApprove": "Oto-Onay", "browser": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "checkpoints": "<PERSON><PERSON><PERSON>", "notifications": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "contextManagement": "Bağlam", "terminal": "Terminal", "completion": "<PERSON><PERSON><PERSON><PERSON>", "prompts": "Promptlar", "experimental": "Den<PERSON>sel", "language": "Dil", "about": "<PERSON><PERSON><PERSON>kı<PERSON>", "interface": "Arayüz"}, "developerMode": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "label": "Gelişti<PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> mod<PERSON>, <PERSON><PERSON><PERSON> özellikler, terminal ayarlar<PERSON>, prompt yönetimi ve daha fazlası dahil olmak üzere gelişmiş özellikler ve ayar seçenekleri sağlar."}, "prompts": {"description": "Prompt geli<PERSON>tirme, kod açıklama ve sorun çözme gibi hızlı eylemler için kullanılan destek promptlarını yapılandırın. <PERSON><PERSON> promptlar, Roo'nun yaygın geliştirme görevleri için daha iyi destek sağlamasına yardımcı olur."}, "codeIndex": {"title": "Kod Tabanı İndeksleme", "enableLabel": "Kod Tabanı İndekslemeyi Etkinleştir", "enableDescription": "Geliştirilmiş arama ve bağlam anlayışı için kod indekslemeyi etkinleştirin", "providerLabel": "Gömme Sağlayıcısı", "selectProviderPlaceholder": "Sağlayıcı seç", "openaiProvider": "OpenAI", "ollamaProvider": "Ollama", "geminiProvider": "Gemini", "geminiApiKeyLabel": "API Anahtarı:", "geminiApiKeyPlaceholder": "Gemini API anahtarınızı girin", "mistralProvider": "<PERSON><PERSON><PERSON>", "mistralApiKeyLabel": "API Anahtarı:", "mistralApiKeyPlaceholder": "Mistral API anahtarınızı girin", "openaiCompatibleProvider": "OpenAI Uyumlu", "openAiKeyLabel": "OpenAI API Anahtarı", "openAiKeyPlaceholder": "OpenAI API anahtarınızı girin", "openAiCompatibleBaseUrlLabel": "Temel URL", "openAiCompatibleApiKeyLabel": "API Anahtarı", "openAiCompatibleApiKeyPlaceholder": "API anahtarınızı girin", "openAiCompatibleModelDimensionLabel": "<PERSON><PERSON><PERSON>u:", "modelDimensionLabel": "Model <PERSON><PERSON><PERSON>", "openAiCompatibleModelDimensionPlaceholder": "örn., 1536", "openAiCompatibleModelDimensionDescription": "<PERSON><PERSON>z i<PERSON><PERSON> gö<PERSON> boyutu (çıktı boyutu). <PERSON><PERSON> <PERSON><PERSON><PERSON> i<PERSON><PERSON> sağ<PERSON>ıcınızın belgelerine bakın. Yaygın değerler: 384, 768, 1536, 3072.", "modelLabel": "Model", "selectModelPlaceholder": "Model seç", "ollamaUrlLabel": "Ollama URL:", "qdrantUrlLabel": "Qdrant URL", "qdrantKeyLabel": "Qdrant <PERSON>:", "startIndexingButton": "<PERSON><PERSON><PERSON>", "clearIndexDataButton": "İndeks Temizle", "unsavedSettingsMessage": "İndeksleme işlemini başlatmadan önce lütfen ayarlarını kaydet.", "clearDataDialog": {"title": "Emin misiniz?", "description": "Bu işlem geri alınamaz. Bu, kod tabanı indeks verilerinizi kalıcı olarak silecektir.", "cancelButton": "İptal", "confirmButton": "<PERSON><PERSON><PERSON><PERSON>"}, "description": "Projenizin anlamsal aramasını etkinleştirmek için kod tabanı indeksleme ayarlarını yapılandırın. <0>Daha fazla bilgi</0>", "statusTitle": "Durum", "settingsTitle": "İndeksleme A<PERSON>ları", "disabledMessage": "Kod tabanı indeksleme şu anda devre dışı. İndeksleme seçeneklerini yapılandırmak için genel ayarlarda etkinleştirin.", "embedderProviderLabel": "Gömücü Sağlayıcı", "modelPlaceholder": "Model adın<PERSON> girin", "selectModel": "Bir model se<PERSON><PERSON>", "ollamaBaseUrlLabel": "Ollama Temel URL", "qdrantApiKeyLabel": "Qdrant API Anahtarı", "qdrantApiKeyPlaceholder": "Qdrant API anahtarınızı girin (isteğe bağlı)", "setupConfigLabel": "<PERSON><PERSON><PERSON>", "ollamaUrlPlaceholder": "http://localhost:11434", "openAiCompatibleBaseUrlPlaceholder": "https://api.example.com", "modelDimensionPlaceholder": "1536", "qdrantUrlPlaceholder": "http://localhost:6333", "saveError": "<PERSON><PERSON><PERSON>", "modelDimensions": "({{dimension}} boyut)", "saveSuccess": "<PERSON><PERSON><PERSON> ba<PERSON><PERSON><PERSON><PERSON> ka<PERSON>", "saving": "Kay<PERSON>ili<PERSON>r...", "saveSettings": "<PERSON><PERSON>", "indexingStatuses": {"standby": "Bekleme", "indexing": "İndeksleniyor", "indexed": "İndekslendi", "error": "<PERSON><PERSON>"}, "close": "Ka<PERSON><PERSON>", "validation": {"invalidQdrantUrl": "Geçersiz Qdrant URL'si", "invalidOllamaUrl": "Geçersiz Ollama URL'si", "invalidBaseUrl": "Geçersiz temel URL'si", "qdrantUrlRequired": "Qdrant URL'si gereklidir", "openaiApiKeyRequired": "OpenAI API anahtarı gereklidir", "modelSelectionRequired": "Model <PERSON><PERSON><PERSON><PERSON>", "apiKeyRequired": "API anahtarı gereklidir", "modelIdRequired": "Model k<PERSON><PERSON><PERSON><PERSON>", "modelDimensionRequired": "Model <PERSON><PERSON><PERSON>", "geminiApiKeyRequired": "Gemini API anahtarı gereklidir", "mistralApiKeyRequired": "Mistral API anahtarı gereklidir", "ollamaBaseUrlRequired": "Ollama temel URL'si gereklidir", "baseUrlRequired": "Temel URL'si gereklidir", "modelDimensionMinValue": "Model boyutu 0'dan b<PERSON>y<PERSON>k olmalıdır"}, "advancedConfigLabel": "Gelişmiş Yapılandırma", "searchMinScoreLabel": "<PERSON><PERSON>", "searchMinScoreDescription": "Arama sonuçları için gereken minimum benzerlik puanı (0.0-1.0). Düşük değerler daha fazla sonuç döndürür ancak daha az alakalı olabilir. Yüksek değerler daha az ancak daha alakalı sonuçlar döndürür.", "searchMinScoreResetTooltip": "Varsayılan değere sıfırla (0.4)", "searchMaxResultsLabel": "<PERSON><PERSON><PERSON><PERSON> Sonuçları", "searchMaxResultsDescription": "Kod tabanı dizinini sorgularken döndürülecek maksimum arama sonucu sayısı. Daha yüksek değerler daha fazla bağlam sağlar ancak daha az alakalı sonuçlar içerebilir.", "resetToDefault": "<PERSON><PERSON><PERSON><PERSON><PERSON> s<PERSON>"}, "autoApprove": {"description": "Roo'nun onay gerektirmeden otomatik olarak işlemler gerçekleştirmesine izin verin. Bu ayarları yalnızca yapay zekaya tamamen güveniyorsanız ve ilgili güvenlik risklerini anlıyorsanız etkinleştirin.", "toggleAriaLabel": "Otomatik onayı değiştir", "disabledAriaLabel": "Otomatik onay devre dışı - önce seçenekleri belirleyin", "readOnly": {"label": "<PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Zhanlu otomatik olarak dizin içeriğini görüntüleyecek ve Onayla düğmesine tıklamanıza gerek kalmadan dosyaları okuyacaktır.", "outsideWorkspace": {"label": "Çalışma alanı dışındaki dosyaları dahil et", "description": "Zhanlu'nun onay gerektirmeden mevcut çalışma alanı dışındaki dosyaları okumasına izin ver."}}, "write": {"label": "<PERSON><PERSON><PERSON>", "description": "Onay gerektirmeden otomatik olarak dosya oluştur ve düzenle", "delayLabel": "Tanılamanın potansiyel sorunları tespit etmesine izin vermek için yazmalardan sonra gecikme", "outsideWorkspace": {"label": "Çalışma alanı dışındaki dosyaları dahil et", "description": "Zhanlu'nun onay gerektirmeden mevcut çalışma alanı dışında dosya oluşturmasına ve düzenlemesine izin ver."}, "protected": {"label": "Korumalı dosyaları dahil et", "description": "Zhanlu'nun korumalı dosyaları (.z<PERSON><PERSON>ign<PERSON> ve .zhanlu/ yap<PERSON><PERSON><PERSON><PERSON> dosyaları gibi) onay gerektirmeden oluşturmasına ve düzenlemesine izin ver."}}, "browser": {"label": "Tarayıcı", "description": "Onay gerektirmeden otomatik olarak tarayıcı eylemleri gerçekleştir. Not: Yalnızca model bilgisayar kullanımını desteklediğinde geçerlidir"}, "retry": {"label": "<PERSON><PERSON><PERSON>", "description": "Sunucu bir hata yanıtı döndürdüğünde başarısız API isteklerini otomatik olarak yeniden dene", "delayLabel": "İsteği yeniden denemeden önce gecikme"}, "mcp": {"label": "MCP", "description": "MCP Sunucuları görünümünde bireysel MCP araçlarının otomatik onayını etkinleştir (hem bu ayar hem de aracın \"Her zaman izin ver\" onay kutusu gerekir)"}, "modeSwitch": {"label": "Mod", "description": "Onay gerektirmeden otomatik olarak farklı modlar arasında geçiş yap"}, "subtasks": {"label": "Alt Gö<PERSON>v<PERSON>", "description": "Onay gerektirmeden alt görevlerin oluşturulmasına ve tamamlanmasına izin ver"}, "followupQuestions": {"label": "<PERSON><PERSON>", "description": "Yapılandırılan zaman aşımından sonra takip sorularına ilişkin ilk önerilen yanıtı otomatik olarak seç", "timeoutLabel": "İlk yanıtı otomatik olarak seçmeden önce beklenecek süre"}, "execute": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Onay gerektirmeden otomatik olarak izin verilen terminal komutlarını yürüt", "allowedCommands": "İzin Verilen Otomatik Yürütme Komutları", "allowedCommandsDescription": "\"Yürüt<PERSON> işlemlerini her zaman onayla\" etkinleştirildiğinde otomatik olarak yürütülebilen komut önekleri. Tüm komutlara izin vermek için * ekleyin (dikkatli kullanın).", "deniedCommands": "Reddedilen komutlar", "deniedCommandsDescription": "Onay istenmeden otomatik olarak reddedilecek komut önekleri. İzin verilen komutlarla çakışma durumunda, en uzun önek eşleşmesi öncelik alır. Tüm komutları reddetmek için * ekleyin.", "commandPlaceholder": "<PERSON><PERSON><PERSON> giri<PERSON> (örn. 'git ')", "deniedCommandPlaceholder": "Reddetmek için komut öneki girin (örn. 'rm -rf')", "addButton": "<PERSON><PERSON>", "autoDenied": "`{{prefix}}` önekli komutlar kullanıcı tarafından yasaklandı. Başka bir komut çalıştırarak bu kısıtlamayı aşma."}, "updateTodoList": {"label": "Todo", "description": "Yapılacaklar listesi onay gerektirmeden otomatik olarak güncellenir"}, "apiRequestLimit": {"title": "<PERSON><PERSON><PERSON>um İstek", "description": "Göreve devam etmek için onay istemeden önce bu sayıda API isteği otomatik olarak yap.", "unlimited": "Sınırsız"}, "selectOptionsFirst": "Otomatik onayı etkinleştirmek için aşağıdan en az bir seçenek seçin"}, "providers": {"providerDocumentation": "{{provider}} Dokümantasyonu", "configProfile": "Yapılandırma Profili", "description": "Sağlayıcılar ve ayarlar arasında hızlıca geçiş yapmak için farklı API yapılandırmalarını kaydedin.", "apiProvider": "API Sağlayıcı", "model": "Model", "nameEmpty": "İsim boş o<PERSON>az", "nameExists": "Bu isme sahip bir profil zaten mevcut", "nameTooLong": "İsim 20 karakterden fazla olamaz", "deleteProfile": "<PERSON><PERSON> sil", "invalidArnFormat": "Geçersiz ARN formatı. Yukarıdaki örnekleri kontrol edin.", "enterNewName": "Yeni ad girin", "addProfile": "<PERSON>il <PERSON>", "renameProfile": "<PERSON><PERSON>", "newProfile": "<PERSON><PERSON> profili", "enterProfileName": "Profil adını girin", "createProfile": "<PERSON><PERSON>", "cannotDeleteOnlyProfile": "Yalnızca tek profili silemezsiniz", "searchPlaceholder": "Profilleri ara", "searchProviderPlaceholder": "Sağlayıcıları ara", "noProviderMatchFound": "Eşleşen sağlayıcı bulunamadı", "noMatchFound": "Eşleşen profil bulunamadı", "vscodeLmDescription": "VS Code Dil Modeli API'si, diğer VS Code uzantıları tarafından sağlanan modelleri çalıştırmanıza olanak tanır (GitHub Copilot dahil ancak bunlarla sınırlı değildir). Başlamanın en kolay yolu, VS Code Marketplace'ten Copilot ve Copilot Chat uzantılarını yüklemektir.", "awsCustomArnUse": "Kullanmak istediğiniz model i<PERSON><PERSON> geçerli bir Amazon Bedrock ARN'si girin. Format örnekleri:", "awsCustomArnDesc": "ARN içindeki bölgenin yukarıda seçilen AWS Bölgesiyle eşleştiğinden emin olun.", "openRouterApiKey": "OpenRouter API Anahtarı", "getOpenRouterApiKey": "OpenRouter API Anahtarı Al", "apiKeyStorageNotice": "API anahtarları VSCode'un Gizli Depolamasında güvenli bir şekilde saklanır", "glamaApiKey": "Glama API Anahtarı", "getGlamaApiKey": "Glama API Anahtarı Al", "useCustomBaseUrl": "Özel temel URL kullan", "useReasoning": "Akıl yü<PERSON><PERSON><PERSON><PERSON>yi etkinleştir", "useHostHeader": "Özel Host başlığ<PERSON> kullan", "useLegacyFormat": "Eski OpenAI API formatını kullan", "customHeaders": "Özel Başlıklar", "headerName": "Başlık adı", "headerValue": "Başlık değeri", "noCustomHeaders": "Tanımlanmış özel başlık yok. Eklemek için + düğmesine tıklayın.", "requestyApiKey": "Requesty API Anahtarı", "refreshModels": {"label": "<PERSON><PERSON><PERSON>", "hint": "En son modelleri görmek için lütfen ayarları yeniden açın.", "loading": "Model listesi yenileniyor...", "success": "Model listesi ba<PERSON><PERSON><PERSON><PERSON> yenilendi!", "error": "Model listesi yenilenemedi. Lütfen tekrar deneyin."}, "getRequestyApiKey": "Requesty API Anahtarı Al", "openRouterTransformsText": "İstem ve mesaj zincirlerini bağlam boyutuna sıkıştır (<a>OpenRouter Dönüşümleri</a>)", "anthropicApiKey": "Zhanlu API Anahtarı", "getAnthropicApiKey": "Zhanlu API Anahtarı Al", "anthropicUseAuthToken": "Anthropic API Anahtarını X-Api-Key yerine Authorization başlığı olarak geçir", "chutesApiKey": "Chutes API Anahtarı", "getChutesApiKey": "Chutes API Anahtarı Al", "deepSeekApiKey": "DeepSeek API Anahtarı", "getDeepSeekApiKey": "DeepSeek API Anahtarı Al", "moonshotApiKey": "Moonshot API Anahtarı", "getMoonshotApiKey": "Moonshot API Anahtarı Al", "moonshotBaseUrl": "Moonshot Giriş Noktası", "geminiApiKey": "Gemini API Anahtarı", "getGroqApiKey": "Groq API Anahtarı Al", "groqApiKey": "Groq API Anahtarı", "getHuggingFaceApiKey": "Hugging Face API Anahtarı Al", "huggingFaceApiKey": "Hugging Face API Anahtarı", "huggingFaceModelId": "Model ID", "huggingFaceLoading": "Yükleniyor...", "huggingFaceModelsCount": "({{count}} model)", "huggingFaceSelectModel": "Bir model seç...", "huggingFaceSearchModels": "Modelleri ara...", "huggingFaceNoModelsFound": "Model bulunamadı", "huggingFaceProvider": "Sağlayıcı", "huggingFaceProviderAuto": "Otomatik", "huggingFaceSelectProvider": "Bir sağlayıcı seç...", "huggingFaceSearchProviders": "Sağlayıcıları ara...", "huggingFaceNoProvidersFound": "Sağlayıcı bulunamadı", "getGeminiApiKey": "Gemini API Anahtarı Al", "openAiApiKey": "OpenAI API Anahtarı", "apiKey": "API Anahtarı", "openAiBaseUrl": "Temel URL", "getOpenAiApiKey": "OpenAI API Anahtarı Al", "mistralApiKey": "Mistral API Anahtarı", "getMistralApiKey": "Mistral / Codestral API Anahtarı Al", "codestralBaseUrl": "Codestral Temel URL (İsteğe bağlı)", "codestralBaseUrlDesc": "Codestral modeli için alternatif URL ayarlayın.", "xaiApiKey": "xAI API Anahtarı", "getXaiApiKey": "xAI API Anahtarı Al", "litellmApiKey": "LiteLLM API Anahtarı", "litellmBaseUrl": "LiteLLM Temel URL", "awsCredentials": "AWS Kimlik Bilgileri", "awsProfile": "AWS Profili", "awsApiKey": "Amazon Bedrock API Anahtarı", "awsProfileName": "AWS Profil Adı", "awsAccessKey": "AWS Erişim <PERSON>", "awsSecretKey": "AWS Gizli Anahtarı", "awsSessionToken": "AWS Oturum Belirteci", "awsRegion": "AWS Bölgesi", "awsCrossRegion": "B<PERSON><PERSON>ler arası çıkarı<PERSON> kullan", "awsBedrockVpc": {"useCustomVpcEndpoint": "Özel VPC uç noktası kullan", "vpcEndpointUrlPlaceholder": "VPC uç noktası URL'sini girin (isteğe bağlı)", "examples": "Örnekler:"}, "enablePromptCaching": "İstem önbelleğini etkinleştir", "enablePromptCachingTitle": "Desteklenen modeller için performansı artırmak ve maliyetleri azaltmak için istem önbelleğini etkinleştir.", "cacheUsageNote": "Not: Önbellek kullanımını görmüyorsanız, fark<PERSON>ı bir model seç<PERSON> ardı<PERSON>n istediğiniz modeli tekrar seçmeyi deneyin.", "vscodeLmModel": "<PERSON><PERSON> Modeli", "vscodeLmWarning": "Not: Bu çok deneysel bir entegrasyondur ve sağlayıcı desteği değişebilir. Bir modelin desteklenmediğine dair bir hata alı<PERSON>nı<PERSON>, bu sağlayıcı tarafındaki bir sorundur.", "googleCloudSetup": {"title": "Google Cloud Vertex AI'yi kullanmak için şunları yapmanız gerekir:", "step1": "1. Google Cloud hesabı oluşturun, Vertex AI API'sini etkinleştirin ve istediğiniz Zhanlu modellerini etkinleştirin.", "step2": "2. Google Cloud CLI'yi yü<PERSON> ve uygulama varsayılan kimlik bilgilerini yapılandırın.", "step3": "3. <PERSON><PERSON><PERSON> kimlik bilgileriyle bir hizmet hesabı oluşturun."}, "googleCloudCredentials": "Google Cloud Kimlik <PERSON>gileri", "googleCloudKeyFile": "Google Cloud Anahtar <PERSON>", "googleCloudProjectId": "Google Cloud Proje <PERSON>", "googleCloudRegion": "Google Cloud Bölgesi", "lmStudio": {"baseUrl": "Temel URL (İsteğe bağlı)", "modelId": "Model <PERSON><PERSON>", "speculativeDecoding": "Spekülatif Kod Çözmeyi Etkinleştir", "draftModelId": "Taslak Model <PERSON>", "draftModelDesc": "Spekülatif kod çözmenin doğru çalışması için taslak model aynı model ailesinden olmalıdır.", "selectDraftModel": "Taslak Model Seç", "noModelsFound": "Taslak model bulunamad<PERSON>. Lütfen LM Studio'nun <PERSON><PERSON><PERSON> et<PERSON>ken çalıştığından emin olun.", "description": "LM Studio, modelleri bilgisayarınızda yerel olarak çalıştırmanıza olanak tanır. Başlamak için <a>hızlı başlangıç kılavuzlarına</a> bakın. Bu uzantıyla kullanmak için LM Studio'nun <b>yerel sunucu</b> özelliğini de başlatmanız gerekecektir. <span>Not:</span> Zhanlu karmaşık istemler kullanır ve Zhanlu modelleriyle en iyi şekilde çalışır. Daha az yetenekli modeller beklendiği gibi çalışmayabilir."}, "ollama": {"baseUrl": "Temel URL (İsteğe bağlı)", "modelId": "Model <PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON>, modelleri bilgisayarınızda yerel olarak çalıştırmanıza olanak tanır. Başlamak için hızlı başlangıç kılavuzlarına bakın.", "warning": "Not: <PERSON><PERSON><PERSON> karmaşık istemler kullanır ve Zhanlu modelleriyle en iyi şekilde çalışır. <PERSON><PERSON> az yetenekli modeller beklendiği gibi çalışmayabilir."}, "unboundApiKey": "Unbound API Anahtarı", "getUnboundApiKey": "Unbound API Anahtarı Al", "unboundRefreshModelsSuccess": "Model listesi güncellendi! Artık en son modeller a<PERSON><PERSON>ndan seçim ya<PERSON>bil<PERSON>.", "unboundInvalidApiKey": "Geçersiz API anahtarı. Lütfen API anahtarınızı kontrol edin ve tekrar deneyin.", "humanRelay": {"description": "API anahtarı gerekmez, ancak kullanıcının bilgileri web sohbet yapay zekasına kopyalayıp yapıştırması gerekir.", "instructions": "Kullanım sırasında bir iletişim kutusu açılacak ve mevcut mesaj otomatik olarak panoya kopyalanacaktır. Bunları web yapay zekalarına (Zhanlu gibi) yapıştırmanız, ardından yapay zekanın yanıtını iletişim kutusuna kopyalayıp onay düğmesine tıklamanız gerekir."}, "openRouter": {"providerRouting": {"title": "OpenRouter Sağlayıcı Yönlendirmesi", "description": "<PERSON><PERSON><PERSON><PERSON>, model<PERSON>z için mevcut en iyi sağlayıcılara istekleri yönlendirir. Varsayılan o<PERSON>, istekler çalışma süresini en üst düzeye çıkarmak için en iyi sağlayıcılar arasında dengelenir. <PERSON><PERSON><PERSON>, bu model i<PERSON><PERSON> kullanılacak belirli bir sağlayıcı seçebilirsiniz.", "learnMore": "Sağlayıcı yönlendirmesi hakkında daha fazla bilgi edinin"}}, "customModel": {"capabilities": "Özel OpenAI uyumlu modelinizin yeteneklerini ve fiyatlandırmasını yapılandırın. Model yeteneklerini belirtirken dikkatl<PERSON> olu<PERSON>, <PERSON><PERSON><PERSON><PERSON> bunlar Zhanlu'un performansını etkileyebilir.", "maxTokens": {"label": "<PERSON><PERSON><PERSON><PERSON> Çıktı Token'ları", "description": "Modelin bir yanıtta üretebileceği maksimum token sayısı. (Sunucunun maksimum token'ları ayarlamasına izin vermek için -1 belirtin.)"}, "contextWindow": {"label": "Bağlam Penceresi Boyutu", "description": "Modelin işleyebileceği toplam token sayısı (giriş + çıkış)."}, "imageSupport": {"label": "Görü<PERSON><PERSON>", "description": "Bu model g<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> işleyip anlayabilir mi?"}, "computerUse": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Bu model bir ta<PERSON><PERSON><PERSON><PERSON><PERSON> etkileşim kurabilir mi? (<PERSON><PERSON><PERSON>)"}, "promptCache": {"label": "İstem Önbelleği", "description": "Bu model is<PERSON><PERSON>i önbelleğe alabilir mi?"}, "pricing": {"input": {"label": "<PERSON><PERSON><PERSON>ı", "description": "G<PERSON>ş/istem ba<PERSON><PERSON>na milyon token maliyeti. Bu, modele bağlam ve talimatlar gönderme maliyetini etkiler."}, "output": {"label": "Çıkış Fiyatı", "description": "Model yanıtı ba<PERSON><PERSON>na milyon token maliyeti. Bu, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> içerik ve tamamlamaların maliyetini etkiler."}, "cacheReads": {"label": "Önbellek Okuma Fiyatı", "description": "Önbellekten okuma başına milyon token maliyeti. Bu, önbelleğe alınmış bir yanıt alındığında uygulanan fiyattır."}, "cacheWrites": {"label": "Önbellek Yazma Fiyatı", "description": "Önbelleğe yazma başına milyon token maliyeti. Bu, bir istem ilk kez önbelleğe alındığında uygulanan fiyattır."}}, "resetDefaults": "Varsayılanlara Sıfırla"}, "rateLimitSeconds": {"label": "<PERSON><PERSON>z sınırı", "description": "API istekleri arasındaki minimum süre."}, "consecutiveMistakeLimit": {"label": "<PERSON><PERSON> ve <PERSON>", "description": "'<PERSON>oo sorun yaşıyor' <PERSON><PERSON><PERSON><PERSON> kutusunu göstermeden önceki ardışık hata veya tekrarlanan eylem sayısı", "unlimitedDescription": "Sınırsız yeniden deneme etkin (otomatik devam et). Diyalog asla görünmeyecek.", "warning": "⚠️ 0'<PERSON> <PERSON><PERSON><PERSON><PERSON>, önemli API kullanımına neden olabilecek sınırsız yeniden denemeye izin verir"}, "reasoningEffort": {"label": "Model Akıl <PERSON>ürütme Çabası", "high": "<PERSON><PERSON><PERSON><PERSON>", "medium": "Orta", "low": "Düşük"}, "setReasoningLevel": "Akıl Yürütme Çabasını Etkinleştir", "claudeCode": {"pathLabel": "<PERSON>", "description": "Claude Code CLI'nize isteğe bağlı yol. Ayarlanmazsa var<PERSON>ılan olarak 'claude' k<PERSON>ı<PERSON>.", "placeholder": "Varsayılan: claude", "maxTokensLabel": "Maks<PERSON>um Çıktı Token sayısı", "maxTokensDescription": "Claude Code yanıtları için maksimum çıktı token sayısı. Varsayılan 8000'dir."}}, "browser": {"enable": {"label": "Tarayıcı aracını etkinleştir", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> bilgisayar kullanımını destekleyen modeller kullanırken web siteleriyle etkileşim kurmak için bir tarayıcı kullanabilir."}, "viewport": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> al<PERSON>u", "description": "Tarayıcı etkileşimleri için görünüm alanı boyutunu seçin. Bu, web sitelerinin nasıl görüntülendiğini ve etkileşime girdiğini etkiler.", "options": {"largeDesktop": "Büyük Masaüstü (1280x800)", "smallDesktop": "Küçük Masaüstü (900x600)", "tablet": "Tablet (768x1024)", "mobile": "Mobil (360x640)"}}, "screenshotQuality": {"label": "Ekran görü<PERSON><PERSON><PERSON><PERSON> ka<PERSON>", "description": "Tarayıcı ekran görüntülerinin WebP kalitesini ayarlayın. Daha yüksek değerler daha net ekran görüntüleri sağlar ancak token kullanımını artırır."}, "remote": {"label": "Uzak tarayıcı bağlantısı kullan", "description": "Uzaktan hata ayıklama etkinleştirilmiş olarak çalışan bir Chrome tarayıcısına bağlanın (--remote-debugging-port=9222).", "urlPlaceholder": "Özel URL (örn. http://localhost:9222)", "testButton": "Bağlantıyı Test Et", "testingButton": "Test Ediliyor...", "instructions": "DevTools protokolü ana bilgisayar adresini girin veya yerel Chrome örneklerini otomatik olarak keşfetmek için boş bırakın. Bağlantıyı Test Et düğmesi, sağlanmışsa özel URL'yi deneyecek veya alan boşsa otomatik olarak keşfedecektir."}}, "checkpoints": {"enable": {"label": "Otomatik kontrol noktalarını etkinleştir", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Zhanlu görev yürütme sırasında otomatik olarak kontrol noktaları oluşturarak değişiklikleri gözden geçirmeyi veya önceki durumlara dönmeyi kolaylaştırır."}}, "notifications": {"sound": {"label": "Ses efektlerini etkinleştir", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> bi<PERSON> ve olaylar için ses efektleri çalacaktır.", "volumeLabel": "<PERSON><PERSON>"}, "tts": {"label": "Metinden sese özelliğini etkinleştir", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> yanıtlarını metinden sese teknolojisi kullanarak sesli okuy<PERSON>ktır.", "speedLabel": "Hız"}}, "contextManagement": {"description": "<PERSON><PERSON>y zekanın bağlam penceresine hangi bilgilerin dahil edileceğini kontrol edin, token kullanımını ve yanıt kalitesini etkiler", "autoCondenseContextPercent": {"label": "Akıllı bağlam sıkıştırmayı tetikleyecek eşik", "description": "Bağlam penceresi bu eşi<PERSON><PERSON> ul<PERSON>ştığında, <PERSON><PERSON><PERSON> otomatik olarak sıkıştıracaktır."}, "condensingApiConfiguration": {"label": "Bağlam Yoğunlaştırma için API Yapılandırması", "description": "Bağlam yoğunlaştırma işlemleri için hangi API yapılandırmasının kullanılacağını seçin. Mevcut aktif yapılandırmayı kullanmak için seçimsiz bırakın.", "useCurrentConfig": "Varsayılan"}, "customCondensingPrompt": {"label": "Özel Bağlam Yoğunlaştırma İstemcisi", "description": "Bağlam yoğunlaştırma için özel sistem istemcisi. Varsayılan istemciyi kullanmak için boş bırakın.", "placeholder": "Özel yoğunlaştırma promptunuzu buraya girin...\n\nVarsayılan prompt ile aynı yapıyı kullanabilirsiniz:\n- Önceki Konuşma\n- Mevcut Çalışma\n- Temel Teknik Kavramlar\n- İlgili Dosyalar ve Kod\n- Problem Çözme\n- Bekleyen Görevler ve Sonraki Adımlar", "reset": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "hint": "Boş = var<PERSON><PERSON><PERSON> promptu kullan"}, "autoCondenseContext": {"name": "Akıllı bağlam sıkıştırmayı otomatik olarak tetikle", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> eşiğe ulaşıldığında bağlamı otomatik olarak sıkıştırır. <PERSON>re dışı bırakıldığında, bağ<PERSON> sıkıştırmayı hala manuel olarak tetikleyebilirsiniz."}, "openTabs": {"label": "Açık sekmeler bağlam sınırı", "description": "Bağlama dahil edilecek maksimum VSCode açık sekme sayısı. Daha yüksek değerler daha fazla bağlam sağlar ancak token kullanımını artırır."}, "workspaceFiles": {"label": "Çalışma alanı dosyaları bağlam sınırı", "description": "Mevcut çalışma dizini ayrıntılarına dahil edilecek maksimum dosya sayısı. Daha yüks<PERSON> değerler daha fazla bağlam sağlar ancak token kullanımını artırır."}, "rooignore": {"label": "Listelerde ve aramalarda .zhanluignore dosyalarını göster", "description": "Etkinleştirildiğ<PERSON>e, .zhanluignore'daki desenlerle eşleşen dosyalar kilit sembolü ile listelerde gösterilecektir. <PERSON>re dışı bırak<PERSON>ld<PERSON>ı<PERSON>, bu dosyalar dosya listelerinden ve aramalardan tamamen gizlenecektir."}, "maxReadFile": {"label": "Dosya okuma otomatik kısaltma eşiği", "description": "Model başlangıç/bitiş değerlerini belirtmediğinde Zhanlu bu sayıda satırı okur. Bu sayı dosyanın toplam satır sayısından azsa, Zhan<PERSON> kod tanımlamalarının satır numarası dizinini oluşturur. Özel durumlar: -1, <PERSON><PERSON><PERSON>'ya tüm dosyayı okumasını (dizinleme olmadan), 0 ise hiç satır okumamasını ve minimum bağlam için yalnızca satır dizinleri sağlamasını belirtir. Düşük değerler başlangıç bağlam kullanımını en aza indirir ve sonraki hassas satır aralığı okumalarına olanak tanır. Açık başlangıç/bitiş istekleri bu ayarla sınırlı değildir.", "lines": "<PERSON>ır", "always_full_read": "Her zaman tüm dosyayı oku"}, "maxConcurrentFileReads": {"label": "Eşzamanlı dosya okuma sınırı", "description": "'read_file' a<PERSON><PERSON><PERSON><PERSON><PERSON> aynı anda işleyebileceği maksimum dosya sayısı. Daha yüksek değerler birden çok küçük dosyanın okunmasını hızlandırabilir ancak bellek kullanımını artırır."}, "diagnostics": {"includeMessages": {"label": "Tanı mesajlarını otomatik olarak bağlama dahil et", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, d<PERSON><PERSON><PERSON>n dosyalardan tanı mesajları (hatalar) otomatik olarak bağlama dahil edilecektir. @problems kullanarak tüm çalışma alanı tanı mesajlarını her zaman manuel olarak dahil edebilirsiniz."}, "maxMessages": {"label": "<PERSON><PERSON><PERSON><PERSON> tanı mesajı", "description": "Do<PERSON>a başına dahil edilecek maksimum tanı mesajı sayısı. Bu sınır hem otomatik dahil etme (onay kutusu etkinleştirildiğinde) hem de manuel @problems bahisleri için geçerlidir. Daha yüksek değerler daha fazla bağlam sağlar ancak jeton kullanımını artırır.", "resetTooltip": "Varsayılan değere sıfırla (50)", "unlimited": "Sınırs<PERSON>z tanı mesajları", "unlimitedLabel": "Sınırsız"}, "delayAfterWrite": {"label": "Potansiyel sorunları tespit etmek için tanılamaya izin vermek üzere yazmalardan sonra gecikme", "description": "Devam etmeden önce dosya yazımlarından sonra beklenecek süre, tanılama araçlarının değişiklikleri işlemesine ve sorunları tespit etmesine olanak tanır."}}, "condensingThreshold": {"label": "Sıkış<PERSON><PERSON><PERSON> Tetikleme Eşiği", "selectProfile": "Profil i<PERSON><PERSON> e<PERSON>ı<PERSON>ı<PERSON>", "defaultProfile": "<PERSON><PERSON><PERSON><PERSON> V<PERSON>ayılan (tüm profiller)", "defaultDescription": "Bağlam bu yüz<PERSON>ye ulaştığında, özel ayarları olmadıkça tüm profiller için otomatik olarak sıkıştırılacak", "profileDescription": "Sadece bu profil i<PERSON><PERSON> (küresel varsayılanı geçersiz kılar)", "inheritDescription": "<PERSON>u profil küresel varsayılan eşiği mi<PERSON> alır ({{threshold}}%)", "usesGlobal": "(küresel {{threshold}}% kullanır)"}}, "terminal": {"basic": {"label": "Terminal Ayarları: Temel", "description": "Temel terminal ayarları"}, "advanced": {"label": "Terminal Ayarları: Gelişmiş", "description": "Aşağıdaki seçeneklerin uygulanması için terminalin yeniden başlatılması gerekebilir."}, "outputLineLimit": {"label": "Terminal çıktısı sınırı", "description": "Komutları yürütürken terminal çıktısına dahil edilecek maksimum satır sayısı. Aşıldığında, token tasarrufu sağlayarak satırlar ortadan kaldırılacaktır. <0>Daha fazla bilgi</0>"}, "outputCharacterLimit": {"label": "Terminal karakter sınırı", "description": "Komutları yürütürken terminal çıktısına dahil edilecek maksimum karakter sayısı. <PERSON><PERSON> sın<PERSON><PERSON>, aşırı uzun satırlardan kaynaklanan bellek sorunlarını önlemek için satır sınırına göre önceliklidir. Aşıldığında, çıktı kesilir. <0>Daha fazla bilgi edinin</0>"}, "shellIntegrationTimeout": {"label": "Terminal kabuk entegrasyonu zaman aşımı", "description": "Komutları yürütmeden önce kabuk entegrasyonunun başlatılması için beklenecek maksimum süre. Kabuk başlatma süresi uzun olan kullanıcılar için, terminalde \"Shell Integration Unavailable\" hatalarını görürseniz bu değerin artırılması gerekebilir. <0>Daha fazla bilgi</0>"}, "shellIntegrationDisabled": {"label": "Terminal kabuk entegrasyonunu devre dışı bırak", "description": "Terminal komutları düzgün çalışmıyorsa veya 'Shell Integration Unavailable' hataları görüyorsanız bunu etkinleştirin. Bu, bazı gelişmiş terminal özelliklerini atlayarak komutları çalıştırmak için daha basit bir yöntem kullanır. <0>Daha fazla bilgi</0>"}, "commandDelay": {"label": "Terminal komut gecikmesi", "description": "Komut yürütmesinden sonra eklenecek gecikme süresi (milisaniye). 0 varsayılan ayarı gecikmeyi tamamen devre dışı bırakır. Bu, zamanlama sorunları olan terminallerde komut çıktısının tam olarak yakalanmasını sağlamaya yardımcı olabilir. Çoğu terminalde bu, `PROMPT_COMMAND='sleep N'` ayarlanarak uygulanır ve PowerShell her komutun sonuna `start-sleep` ekler. Başlangıçta VSCode hata#237208 için bir geçici çözümdü ve gerekli olmayabilir. <0>Daha fazla bilgi</0>"}, "compressProgressBar": {"label": "İlerleme çubuğu çıktısını sıkıştır", "description": "<PERSON><PERSON><PERSON><PERSON>ş<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, satır başı karakteri (\\r) içeren terminal çıktısını işleyerek gerçek bir terminalin içeriği nasıl göstereceğini simüle eder. Bu, <PERSON><PERSON><PERSON><PERSON> ara durumlarını kaldırır, yaln<PERSON><PERSON><PERSON> son durumu korur ve daha alakalı bilgiler için bağlam alanından tasarruf sağlar. <0>Daha fazla bilgi</0>"}, "powershellCounter": {"label": "PowerShell sayaç geçici çözümünü etkinleştir", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, komutların doğru şekilde yürütülmesini sağlamak için PowerShell komutlarına bir sayaç ekler. Bu, çıktı yakalama sorunları yaşayabilecek PowerShell terminallerinde yardımcı olur. <0>Daha fazla bilgi</0>"}, "zshClearEolMark": {"label": "ZSH satır sonu işaretini temizle", "description": "<PERSON>t<PERSON>leş<PERSON>rildiğ<PERSON><PERSON>, PROMPT_EOL_MARK='' ayarlanarak ZSH satır sonu işaretini temizler. Bu, '%' gibi özel karakterlerle biten komut çıktılarının yorumlanmasında sorun yaşanmasını önler. <0>Daha fazla bilgi</0>"}, "zshOhMy": {"label": "Oh My Zsh entegrasy<PERSON><PERSON><PERSON> et<PERSON>", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Oh My Zsh kabuk entegrasyon özelliklerini etkinleştirmek için ITERM_SHELL_INTEGRATION_INSTALLED=Yes ayarlar. Bu ayarın uygulanması IDE'nin yeniden başlatılmasını gerektirebilir. <0>Daha fazla bilgi</0>"}, "zshP10k": {"label": "Powerlevel10k entegrasyonunu etkinleştir", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Powerlevel10k kabuk entegrasyon özelliklerini etkinleştirmek için POWERLEVEL9K_TERM_SHELL_INTEGRATION=true ayarlar. <0>Daha fazla bilgi</0>"}, "zdotdir": {"label": "ZDOTDIR işlemeyi etkinleştir", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, zsh kabuğu entegrasyonunu düzgün şekilde işlemek için ZDOTDIR için geçici bir dizin oluşturur. Bu, zsh yapılandırmanızı korurken VSCode kabuk entegrasyonunun zsh ile düzgün çalışmasını sağlar. <0>Daha fazla bilgi</0>"}, "inheritEnv": {"label": "Ortam <PERSON>ğ<PERSON>şkenlerini devral", "description": "Etkinleştirildiğinde, terminal VSCode üst işleminden ortam değişkenlerini devralır, <PERSON><PERSON><PERSON><PERSON> kullanıcı profilinde tanımlanan kabuk entegrasyon ayarları gibi. Bu, VSCode'un global ayarı olan `terminal.integrated.inheritEnv` değerini doğrudan değiştirir. <0>Daha fazla bilgi</0>"}}, "advancedSettings": {"title": "Gelişmiş ayarlar"}, "advanced": {"diff": {"label": "Diff'ler <PERSON>ığıyla düzenlemeyi etkinleştir", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> dosyaları daha hızlı düzenleyebilecek ve kesik tam dosya yazımlarını otomatik olarak reddedecektir. En son <PERSON><PERSON><PERSON> modeliyle en iyi şekilde çalışır.", "strategy": {"label": "<PERSON><PERSON> strate<PERSON>", "options": {"standard": "Standart (Tek blok)", "multiBlock": "Deneysel: Ç<PERSON><PERSON> blok diff", "unified": "Deneysel: Birleştirilmiş diff"}, "descriptions": {"standard": "Standart diff strate<PERSON><PERSON>, bir seferde tek bir kod bloğuna değişiklikler uygular.", "unified": "Birleştirilmiş diff strate<PERSON><PERSON>, diff'leri u<PERSON> için birden çok yaklaşım benimser ve en iyi yaklaşımı seçer.", "multiBlock": "Çoklu blok diff stratejisi, tek bir istekte bir dosyadaki birden çok kod bloğunu güncellemenize olanak tanır."}}, "matchPrecision": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Bu kaydırı<PERSON><PERSON>, diff'ler uygulanırken kod bölümlerinin ne kadar hassas bir şekilde eşleşmesi gerektiğini kontrol eder. Daha düşük değerler daha esnek eşleşmeye izin verir ancak yanlış değiştirme riskini artırır. %100'ün altındaki değerleri son derece dikkatli kullanın."}}, "todoList": {"label": "Yapılacaklar listesi aracını etkinleştir", "description": "Etkinleş<PERSON><PERSON><PERSON><PERSON>e, zhanlu görev ilerlemesini takip etmek için yapılacaklar listeleri oluşturabilir ve yönetebilir. Bu, karmaşık görevleri yönetilebilir adımlara organize etmeye yardımcı olur."}}, "completion": {"description": "Kod tama<PERSON>lama ayarlarını yapılandırarak geliştirme deneyiminizi geliştirin.", "configureButton": "Yapılandır", "debounceTime": {"label": "<PERSON><PERSON><PERSON><PERSON> gec<PERSON>i", "description": "<PERSON><PERSON> tama<PERSON>a tetikleyicisi için g<PERSON> (milisaniye)"}, "number": {"label": "<PERSON><PERSON><PERSON><PERSON> sayı<PERSON>ı", "description": "Oluşturulacak kod tamamlama adaylarının sayısı"}, "granularity": {"label": "Tamamlama ayrıntı düzeyi tercihi", "description": "Kod tamamlama için ayrıntı düzeyi tercihi ayarı", "singleRow": "Tek <PERSON>ır", "oneTimeMaximization": "Tek seferlik maksimizasyon", "balanced": "<PERSON><PERSON><PERSON>"}, "multipleLineCompletion": {"label": "Çok satırlı tamamlama modu", "description": "Çok satırlı kod tamamlama için tetikleyici modu", "autoCompletion": "Otomatik tama<PERSON>a", "triggerCompletion": "<PERSON><PERSON><PERSON><PERSON> tamamlama"}, "maxTokens": {"label": "Maks<PERSON>um token sayısı", "description": "Kod tama<PERSON>a i<PERSON>in maksimum token sayısı"}}, "experimental": {"DIFF_STRATEGY_UNIFIED": {"name": "Deneysel birleştirilmiş diff stratejisini kullan", "description": "Deneysel birleştirilmiş diff stratejisini etkinleştir. <PERSON>u strateji, model hat<PERSON><PERSON><PERSON><PERSON><PERSON> kaynaklanan yeniden deneme sayısını azaltabilir, ancak beklenmeyen davranışlara veya hatalı düzenlemelere neden olabilir. Yalnızca riskleri anlıyorsanız ve tüm değişiklikleri dikkatlice incelemeye istekliyseniz etkinleştirin."}, "SEARCH_AND_REPLACE": {"name": "Deneysel arama ve değiştirme aracını kullan", "description": "Deneysel arama ve değiştirme aracını etkinleştir, Zhanlu'nun tek bir istekte bir arama teriminin birden fazla örneğini değiştirmesine olanak tanır."}, "INSERT_BLOCK": {"name": "Deneysel içerik ekleme aracını kullan", "description": "Deneysel içerik ekleme aracını etkinleştir, <PERSON><PERSON><PERSON>'nun bir diff oluşturma gereği duymadan belirli satır numaralarına içerik eklemesine olanak tanır."}, "POWER_STEERING": {"name": "<PERSON><PERSON><PERSON> \"g<PERSON><PERSON>\" modunu kullan", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Zhanlu modele geçerli mod tanımının ayrıntılarını daha sık hatırlatacaktır. Bu, rol tanımlarına ve özel talimatlara daha güçlü uyum sağlayacak, ancak mesaj başına daha fazla token kullanacaktır."}, "MULTI_SEARCH_AND_REPLACE": {"name": "Deneysel çoklu blok diff aracını kullan", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> çoklu blok diff aracını kullanacaktır. <PERSON><PERSON>, tek bir istekte dosyadaki birden fazla kod bloğunu güncellemeye çalışacaktır."}, "CONCURRENT_FILE_READS": {"name": "Eşzamanlı dosya okumayı etkinleştir", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, zhan<PERSON> tek bir istekte birden fazla dosya okuyabilir. <PERSON><PERSON> dışı bırakıldı<PERSON>ı<PERSON>, zhan<PERSON> dosyaları birer birer okumalıdır. <PERSON>ha az yetenekli modellerle çalışırken veya dosya erişimi üzerinde daha fazla kontrol istediğinizde devre dışı bırakmak yardımcı olabilir."}, "MARKETPLACE": {"name": "Marketplace'i Etkinleştir", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Marketplace'ten MCP'leri ve özel modları yükleyebilirsiniz."}, "MULTI_FILE_APPLY_DIFF": {"name": "Eşzamanlı dosya düzenlemelerini etkinleştir", "description": "<PERSON><PERSON><PERSON><PERSON>ş<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, zhan<PERSON> tek bir istekte birden fazla dosyayı düzenleyebilir. <PERSON>re dışı bırakıldığında, zhan<PERSON> dosyaları tek tek düzenlemek zorundadır. <PERSON>unu devre dış<PERSON> bı<PERSON>k, daha az yetenekli modellerle çalışırken veya dosya değişiklikleri üzerinde daha fazla kontrol istediğinde yardımcı olabilir."}}, "promptCaching": {"label": "Prompt önbelleğini devre dışı bırak", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> bu model <PERSON><PERSON><PERSON> prompt <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> kullanmayacaktır."}, "temperature": {"useCustom": "<PERSON>zel sıcaklık kullan", "description": "Model yan<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> rastgeleliği kontrol eder.", "rangeDescription": "Daha yüksek değerler çıktıyı daha rastgele ya<PERSON>, daha dü<PERSON><PERSON><PERSON> değerler daha deterministik hale getirir."}, "modelInfo": {"supportsImages": "Görüntüleri destekler", "noImages": "Görüntüleri desteklemez", "supportsComputerUse": "Bilgisayar kullanımını destekler", "noComputerUse": "Bilgisayar kullanımını desteklemez", "supportsPromptCache": "İstem önbelleğini destekler", "noPromptCache": "İstem önbelleğini desteklemez", "maxOutput": "<PERSON><PERSON><PERSON><PERSON> ç<PERSON>ı", "inputPrice": "<PERSON><PERSON><PERSON> fi<PERSON>tı", "outputPrice": "Çıkış fiyatı", "cacheReadsPrice": "Önbellek okuma fiyatı", "cacheWritesPrice": "Önbellek yazma fiyatı", "enableStreaming": "Akışı etkinleştir", "enableR1Format": "R1 model parametrel<PERSON><PERSON>r", "enableR1FormatTips": "QWQ gibi R1 modelleri kullanıldığında etkinleştirilmelidir, 400 hatası alınmaması için", "useAzure": "Azure kullan", "azureApiVersion": "Azure API sürümünü ayarla", "gemini": {"freeRequests": "* Dakikada {{count}} isteğe kadar ücretsiz. Bundan sonra, ücretlendirme istem boyutuna bağlıdır.", "pricingDetails": "Daha fazla bilgi için fiyatlandırma ayrıntılarına bakın.", "billingEstimate": "* Ücretlendirme bir tahmindir - kesin maliyet istem boyutuna bağlıdır."}}, "modelPicker": {"automaticFetch": "Uzantı <serviceLink>{{serviceName}}</serviceLink> üzerinde bulunan mevcut modellerin en güncel listesini otomatik olarak alır. Hangi modeli seçeceğinizden emin değilseniz, Zhanlu <defaultModelLink>{{defaultModelId}}</defaultModelLink> ile en iyi şekilde çalışır. Şu anda mevcut olan ücretsiz seçenekleri bulmak için \"free\" araması da yapabilirsiniz.", "label": "Model", "searchPlaceholder": "Ara", "noMatchFound": "Eşleşme bulunamadı", "useCustomModel": "<PERSON><PERSON> kull<PERSON>: {{modelId}}"}, "footer": {"feedback": "<PERSON><PERSON><PERSON> bir sorunuz veya geri bild<PERSON><PERSON>, lüt<PERSON> <qqDocsLink>bir sorun bildirin</qqDocsLink>.", "version": "<PERSON><PERSON><PERSON> v{{version}}", "telemetry": {"label": "<PERSON><PERSON>m hata ve kullanım raporlamaya izin ver", "description": "Anonim kullanım verileri ve hata raporları göndererek Zhanlu'u geliştirmeye yardımcı olun. <PERSON>ç<PERSON> kod, istem veya kişisel bilgi asla gönderilmez. Daha fazla ayrıntı için gizlilik politikamıza bakın."}, "settings": {"import": "İçe Aktar", "export": "Dışa Aktar", "reset": "Sıfırla"}}, "thinkingBudget": {"maxTokens": "Ma<PERSON><PERSON><PERSON> token", "maxThinkingTokens": "<PERSON><PERSON><PERSON><PERSON>"}, "validation": {"apiKey": "Geçerli bir API anahtarı sağlamalısınız.", "awsRegion": "Amazon Bedrock kullanmak için bir bölge seçmelisiniz.", "googleCloud": "Geçerli bir Google Cloud proje kimliği ve bölge sağlamalısınız.", "modelId": "Geçerli bir model k<PERSON><PERSON><PERSON><PERSON>ını<PERSON>.", "modelSelector": "Geçerli bir model se<PERSON><PERSON> sağlamalısınız.", "openAi": "Geçerli bir temel URL, API anahtarı ve model kimliği sağlamalısınız.", "arn": {"invalidFormat": "Geçersiz ARN formatı. Lütfen format gereksinimlerini kontrol edin.", "regionMismatch": "Uyarı: ARN'nizdeki bölge ({{arnRegion}}) seçtiğiniz bölge ({{region}}) ile eşleşmiyor. Bu eri<PERSON>im sorunlarına neden olabilir. Sağlayıcı, ARN'deki bölgeyi kullanacak."}, "modelAvailability": "Sağladığın<PERSON>z model kimliği ({{modelId}}) kullanılamıyor. Lütfen başka bir model seçin.", "providerNotAllowed": "Sağlayıcı '{{provider}}' kuruluşunuz tarafından izin verilmiyor", "modelNotAllowed": "Model '{{model}}' sağlayıcı '{{provider}}' i<PERSON><PERSON> k<PERSON>uz tarafından izin verilmiyor", "profileInvalid": "<PERSON><PERSON> profil, kuruluşunuz tarafından izin verilmeyen bir sağlayıcı veya model içeriyor"}, "placeholders": {"apiKey": "API anahtarını girin...", "profileName": "Profil adını girin", "accessKey": "<PERSON><PERSON><PERSON><PERSON> girin...", "secretKey": "G<PERSON><PERSON> anahtarı girin...", "sessionToken": "<PERSON><PERSON><PERSON> beli<PERSON>cini girin...", "credentialsJson": "Kimlik bilgileri JSON'ını girin...", "keyFilePath": "<PERSON><PERSON><PERSON> yolunu girin...", "projectId": "Proje ID'sini girin...", "customArn": "ARN girin (örn. arn:aws:bedrock:us-east-1:123456789012:foundation-model/my-model)", "baseUrl": "Temel URL'yi girin...", "modelId": {"lmStudio": "örn. meta-llama-3.1-8b-instruct", "lmStudioDraft": "örn. lmstudio-community/llama-3.2-1b-instruct", "ollama": "örn. llama3.1"}, "numbers": {"maxTokens": "örn. 4096", "contextWindow": "örn. 128000", "inputPrice": "örn. 0.0001", "outputPrice": "örn. 0.0002", "cacheWritePrice": "örn. 0.00005"}}, "defaults": {"ollamaUrl": "Varsayılan: http://localhost:11434", "lmStudioUrl": "Varsayılan: http://localhost:1234", "geminiUrl": "Varsayılan: https://generativelanguage.googleapis.com"}, "labels": {"customArn": "Özel <PERSON>", "useCustomArn": "Özel ARN kullan..."}, "includeMaxOutputTokens": "<PERSON><PERSON><PERSON><PERSON> çıktı tokenlerini dahil et", "includeMaxOutputTokensDescription": "API isteklerinde maksimum çıktı token parametresini gönder. Bazı sağlayıcılar bunu desteklemeyebilir.", "limitMaxTokensDescription": "Yanıttaki maksimum token sayısını sınırla", "maxOutputTokensLabel": "<PERSON><PERSON><PERSON><PERSON> çıktı tokenları", "maxTokensGenerateDescription": "Yanıtta oluşturulacak maksimum token sayısı"}