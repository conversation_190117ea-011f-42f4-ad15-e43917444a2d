{"common": {"save": "<PERSON><PERSON><PERSON><PERSON>", "done": "<PERSON><PERSON><PERSON>", "cancel": "<PERSON><PERSON><PERSON>", "reset": "<PERSON><PERSON><PERSON><PERSON>", "select": "<PERSON><PERSON><PERSON><PERSON>", "add": "<PERSON><PERSON><PERSON>", "remove": "Usuń"}, "header": {"title": "Ustawienia", "saveButtonTooltip": "Zapisz zmiany", "nothingChangedTooltip": "<PERSON>c się nie zmieniło", "doneButtonTooltip": "Odrzuć niezapisane zmiany i zamknij panel ustawień"}, "unsavedChangesDialog": {"title": "Niezapisane zmiany", "description": "<PERSON><PERSON><PERSON>z odrzucić zmiany i kontynuować?", "cancelButton": "<PERSON><PERSON><PERSON>", "discardButton": "<PERSON><PERSON><PERSON><PERSON>"}, "sections": {"providers": "<PERSON><PERSON><PERSON><PERSON>", "autoApprove": "Auto-zatwierdzanie", "browser": "Dostęp komputera", "checkpoints": "<PERSON><PERSON> kontrolne", "notifications": "Powiadomienia", "contextManagement": "Kontekst", "terminal": "Terminal", "prompts": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "completion": "Uzupełnianie", "experimental": "Eksperymentalne", "language": "Język", "about": "<PERSON>", "interface": "Interfejs"}, "developerMode": {"title": "Tryb programisty", "label": "Włącz tryb programisty", "description": "Tryb programisty zapewnia zaawansowane funkcje i opcje konfiguracji, w tym funkcje eksperymentalne, ustawienia terminala, zarządzanie podpowiedziami i więcej."}, "prompts": {"description": "Skonfiguruj podpowiedzi wsparcia używane do szybkich działań, takich jak ulepszanie podpowiedzi, wyjaśnianie kodu i rozwiązywanie problemów. Te podpowiedzi pomagają zhanlu zapewnić lepsze wsparcie dla typowych zadań programistycznych."}, "codeIndex": {"title": "Indeksowanie kodu", "enableLabel": "Włącz indeksowanie kodu", "enableDescription": "Włącz indeksowanie kodu, aby <PERSON><PERSON>ić wyszukiwanie i zrozumienie kontekstu", "providerLabel": "Dostawca osadzania", "selectProviderPlaceholder": "<PERSON><PERSON><PERSON><PERSON>", "openaiProvider": "OpenAI", "ollamaProvider": "Ollama", "geminiProvider": "Gemini", "geminiApiKeyLabel": "Klucz API:", "geminiApiKeyPlaceholder": "Wprowadź swój klucz API Gemini", "mistralProvider": "<PERSON><PERSON><PERSON>", "mistralApiKeyLabel": "Klucz API:", "mistralApiKeyPlaceholder": "Wprowadź swój klucz API Mistral", "openaiCompatibleProvider": "Kompatybilny z OpenAI", "openAiKeyLabel": "Klucz API OpenAI", "openAiKeyPlaceholder": "Wprowadź swój klucz API OpenAI", "openAiCompatibleBaseUrlLabel": "Bazowy URL", "openAiCompatibleApiKeyLabel": "Klucz API", "openAiCompatibleApiKeyPlaceholder": "Wprowadź swój klucz API", "openAiCompatibleModelDimensionLabel": "<PERSON><PERSON><PERSON>:", "modelDimensionLabel": "<PERSON><PERSON><PERSON>u", "openAiCompatibleModelDimensionPlaceholder": "np., 1536", "openAiCompatibleModelDimensionDescription": "<PERSON><PERSON><PERSON> embed<PERSON> (roz<PERSON><PERSON> w<PERSON>) dla twojego modelu. Sprawdź dokumentację swojego dostawcy, aby u<PERSON><PERSON><PERSON> tę wartość. Typowe wartości: 384, 768, 1536, 3072.", "modelLabel": "Model", "selectModelPlaceholder": "Wybierz model", "ollamaUrlLabel": "URL Ollama:", "qdrantUrlLabel": "URL Qdrant", "qdrantKeyLabel": "<PERSON><PERSON>cz Qdrant:", "startIndexingButton": "Rozpocznij", "clearIndexDataButton": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> indeks", "unsavedSettingsMessage": "Zapisz swoje ustawienia przed rozpoczęciem procesu indeksowania.", "clearDataDialog": {"title": "<PERSON><PERSON> j<PERSON> p<PERSON>?", "description": "<PERSON>j akcji nie można cofnąć. Spowoduje to trwałe usunięcie danych indeksu Twojego kodu.", "cancelButton": "<PERSON><PERSON><PERSON>", "confirmButton": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> dane"}, "description": "Skonfiguruj ustawienia indeksowania bazy kodu, aby w<PERSON><PERSON><PERSON><PERSON>ć wyszukiwanie semantyczne w swoim projekcie. <0>Dowiedz się więcej</0>", "statusTitle": "Status", "settingsTitle": "Ustawienia indeksowania", "disabledMessage": "Indeksowanie bazy kodu jest obecnie wyłączone. Włącz je w ustawieniach globalnych, aby skon<PERSON><PERSON><PERSON><PERSON> opcje indeksowania.", "embedderProviderLabel": "Dostawca Embeddera", "modelPlaceholder": "Wprowadź nazwę modelu", "selectModel": "Wybierz model", "ollamaBaseUrlLabel": "Bazowy URL Ollama", "qdrantApiKeyLabel": "Klucz API Qdrant", "qdrantApiKeyPlaceholder": "Wprowadź swój klucz API Qdrant (opcjonalnie)", "setupConfigLabel": "Konfiguracja", "ollamaUrlPlaceholder": "http://localhost:11434", "openAiCompatibleBaseUrlPlaceholder": "https://api.example.com", "modelDimensionPlaceholder": "1536", "qdrantUrlPlaceholder": "http://localhost:6333", "saveError": "<PERSON>e udało się zapisać ustawień", "modelDimensions": "({{dimension}} wymiarów)", "saveSuccess": "Ustawienia zapisane pomyślnie", "saving": "Zapisywanie...", "saveSettings": "<PERSON><PERSON><PERSON><PERSON>", "indexingStatuses": {"standby": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "indexing": "Indeksowanie", "indexed": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "error": "Błąd"}, "close": "Zamknij", "validation": {"invalidQdrantUrl": "Nieprawidłowy URL Qdrant", "invalidOllamaUrl": "Nieprawidłowy URL Ollama", "invalidBaseUrl": "Nieprawidłowy podstawowy URL", "qdrantUrlRequired": "<PERSON><PERSON><PERSON><PERSON> jest URL Qdrant", "openaiApiKeyRequired": "Wymagany jest klucz API OpenAI", "modelSelectionRequired": "W<PERSON><PERSON><PERSON> jest wyb<PERSON>r modelu", "apiKeyRequired": "W<PERSON><PERSON>y jest klucz API", "modelIdRequired": "<PERSON><PERSON><PERSON>e jest ID modelu", "modelDimensionRequired": "Wym<PERSON><PERSON> jest wymiar modelu", "geminiApiKeyRequired": "Wymagany jest klucz API Gemini", "mistralApiKeyRequired": "Klucz API Mistral jest wymagany", "ollamaBaseUrlRequired": "W<PERSON><PERSON>y jest bazowy adres URL Ollama", "baseUrlRequired": "W<PERSON><PERSON>y jest bazowy adres URL", "modelDimensionMinValue": "Wymiar modelu musi być większy niż 0"}, "advancedConfigLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "searchMinScoreLabel": "Próg wyniku wyszukiwania", "searchMinScoreDescription": "Minimalny wynik podobieństwa (0.0-1.0) wymagany dla wyników wyszukiwania. Niższe wartości zwracają więcej wyników, ale mogą być mniej trafne. Wyższe wartości zwracają mniej wyników, ale bardziej trafnych.", "searchMinScoreResetTooltip": "Zresetuj do wartości domyślnej (0.4)", "searchMaxResultsLabel": "Maksymalna liczba wyników wyszukiwania", "searchMaxResultsDescription": "Maksymalna liczba wyników wyszukiwania zwracanych podczas zapytania do indeksu bazy kodu. Wyższe wartości zapewniają więcej kontekstu, ale mogą zawierać mniej istotne wyniki.", "resetToDefault": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "autoApprove": {"description": "Pozwól zhanlu na automatyczne wykonywanie operacji bez wymagania zatwierdzenia. Włącz te ustawienia tylko jeśli w pełni ufasz AI i rozumiesz związane z tym zagrożenia bezpieczeństwa.", "toggleAriaLabel": "Przełącz automatyczne zatwierdzanie", "disabledAriaLabel": "Automatyczne zatwierdzanie wyłączone - najpierw wybierz opcje", "readOnly": {"label": "<PERSON><PERSON><PERSON><PERSON>", "description": "<PERSON>dy włączone, Zhanlu automatycznie będzie wyświetlać zawartość katalogów i czytać pliki bez konieczności klikania przycisku Zatwierdź.", "outsideWorkspace": {"label": "Uwzględnij pliki poza obszarem roboczym", "description": "Pozwól Zhanlu na odczyt plików poza bieżącym obszarem roboczym bez konieczności zatwierdzania."}, "protected": {"label": "Uwzględnij pliki chronione", "description": "Pozwól Zhanlu na tworzenie i edycję plików chronionych (takich jak .zhanluignore i pliki konfiguracyjne .zhanlu/) bez konieczności zatwierdzania."}}, "write": {"label": "<PERSON>ap<PERSON>", "description": "Automatycznie twórz i edytuj pliki bez konieczności zatwierdzania", "delayLabel": "Opóźnienie po zap<PERSON>ch, aby um<PERSON>ż<PERSON>wić diagnostyce wykrycie potencjalnych problemów", "outsideWorkspace": {"label": "Uwzględnij pliki poza obszarem roboczym", "description": "Pozwól Zhanlu na tworzenie i edycję plików poza bieżącym obszarem roboczym bez konieczności zatwierdzania."}}, "browser": {"label": "Przeglądarka", "description": "Automatycznie wykonuj akcje przeglądarki bez konieczności zatwierdzania. Uwaga: Dotyczy tylko gdy model obsługuje używanie komputera"}, "retry": {"label": "Ponów", "description": "Automatycznie ponawiaj nieudane żądania API, gdy serwer zwraca odpowiedź z błędem", "delayLabel": "Opóźnienie przed ponowieniem żądania"}, "mcp": {"label": "MCP", "description": "Włącz automatyczne zatwierdzanie poszczególnych narzędzi MCP w widoku Serwerów MCP (wymaga zarówno tego ustawienia, jak i pola wyboru \"Zawsze zezwalaj\" narzędzia)"}, "modeSwitch": {"label": "<PERSON><PERSON>", "description": "Automatycznie przełączaj między różnymi trybami bez konieczności zatwierdzania"}, "subtasks": {"label": "Podzadania", "description": "Zezwalaj na tworzenie i ukończenie podzadań bez konieczności zatwierdzania"}, "followupQuestions": {"label": "<PERSON><PERSON><PERSON><PERSON>", "description": "Automatycznie wybierz pierwszą sugerowaną odpowiedź na pytania uzupełniające po skonfigurowanym limicie czasu", "timeoutLabel": "<PERSON>zas oczekiwania przed automatycznym wybraniem pierwszej odpowiedzi"}, "execute": {"label": "<PERSON><PERSON><PERSON><PERSON>", "description": "Automatycznie wykonuj dozwolone polecenia terminala bez konieczności zatwierdzania", "allowedCommands": "Dozwolone polecenia auto-wykonania", "allowedCommandsDescription": "Prefi<PERSON>y pole<PERSON>ń, kt<PERSON>re mogą być automatycznie wykonywane, g<PERSON> \"Zawsze zatwierdzaj operacje wykonania\" jest włączone. Dodaj * aby zezwolić na wszystkie polecenia (używaj z ostrożnością).", "deniedCommands": "Odrzucone polecenia", "deniedCommandsDescription": "Prefiksy pole<PERSON>ń, kt<PERSON>re będą automatycznie odrzucane bez pytania o zatwierdzenie. W przypadku konfliktów z dozwolonymi poleceniami, najdłuższe dopasowanie prefiksu ma pierwszeństwo. Dodaj * aby odr<PERSON><PERSON>ć wszystkie polecenia.", "commandPlaceholder": "Wprowadź prefiks polecenia (np. 'git ')", "deniedCommandPlaceholder": "Wprowadź prefiks polecenia do odrzucenia (np. 'rm -rf')", "addButton": "<PERSON><PERSON><PERSON>", "autoDenied": "Polecenia z prefiksem `{{prefix}}` zostały zabronione przez użytkownika. Nie obchodź tego ograniczenia uruchamiając inne polecenie."}, "updateTodoList": {"label": "Todo", "description": "Lista zadań jest automatycznie aktualizowana bez zatwierdzenia"}, "apiRequestLimit": {"title": "Maksymalna liczba żądań", "description": "Automatycznie wykonaj tyle żądań API przed poproszeniem o zgodę na kontynuowanie zadania.", "unlimited": "<PERSON><PERSON> limitu"}, "selectOptionsFirst": "Wybierz co najmniej jedną opcję poniżej, aby włączyć automatyczne zatwierdzanie"}, "providers": {"providerDocumentation": "Dokumentacja {{provider}}", "configProfile": "<PERSON><PERSON>", "description": "Zapisz różne konfiguracje API, aby szybko przełączać się między dostawcami i ustawieniami.", "apiProvider": "Dostawca API", "model": "Model", "nameEmpty": "Nazwa nie może by<PERSON> pusta", "nameExists": "Profil o tej nazwie już istnieje", "nameTooLong": "Nazwa nie może być dłuższa niż 20 znaków", "deleteProfile": "Usuń profil", "invalidArnFormat": "Nieprawidłowy format ARN. Sprawdź powyższe przykłady.", "enterNewName": "Wprowadź nową nazwę", "addProfile": "<PERSON><PERSON><PERSON> profil", "renameProfile": "Zmień nazwę profilu", "newProfile": "<PERSON><PERSON> profil kon<PERSON>", "enterProfileName": "Wprowadź nazwę profilu", "createProfile": "Utwórz profil", "cannotDeleteOnlyProfile": "<PERSON><PERSON> mo<PERSON>na <PERSON> jedynego profilu", "searchPlaceholder": "<PERSON><PERSON><PERSON> profili", "searchProviderPlaceholder": "Szukaj dostawców", "noProviderMatchFound": "Nie znaleziono dostawców", "noMatchFound": "Nie znaleziono pasujących profili", "vscodeLmDescription": "Interfejs API modelu językowego VS Code umożliwia uruchamianie modeli dostarczanych przez inne rozszerzenia VS Code (w tym, ale nie tylko, GitHub Copilot). Najłatwiejszym sposobem na rozpoczęcie jest zainstalowanie rozszerzeń Copilot i Copilot Chat z VS Code Marketplace.", "awsCustomArnUse": "Wprowadź prawidłowy Amazon Bedrock ARN dla modelu, którego chcesz użyć. Przykłady formatu:", "awsCustomArnDesc": "Upewnij się, że region w ARN odpowiada wybranemu powyżej regionowi AWS.", "openRouterApiKey": "Klucz API OpenRouter", "getOpenRouterApiKey": "Uzyskaj klucz API OpenRouter", "apiKeyStorageNotice": "Klucze API są bezpiecznie przechowywane w Tajnym Magazynie VSCode", "glamaApiKey": "Klucz API Glama", "getGlamaApiKey": "Uzyskaj klucz API Glama", "useCustomBaseUrl": "Użyj niestandardowego URL bazowego", "useReasoning": "Włącz rozumowanie", "useHostHeader": "Użyj niestandardowego nagłówka Host", "useLegacyFormat": "Użyj starszego formatu API OpenAI", "customHeaders": "Niestandardowe nagłówki", "headerName": "Nazwa nagłówka", "headerValue": "Wartość nagłówka", "noCustomHeaders": "Brak zdefiniowanych niestandardowych nagłówków. Kliknij przycisk +, aby do<PERSON>.", "requestyApiKey": "Klucz API Requesty", "refreshModels": {"label": "Odświ<PERSON>ż modele", "hint": "Proszę ponownie otworzyć ustawienia, aby z<PERSON><PERSON><PERSON><PERSON> najn<PERSON>ze modele.", "loading": "Odświeżanie listy modeli...", "success": "Lista modeli została pomyślnie odświeżona!", "error": "Nie udało się odświeżyć listy modeli. Spróbuj ponownie."}, "getRequestyApiKey": "Uzyskaj klucz API Requesty", "openRouterTransformsText": "Kompresuj podpowiedzi i łańcuchy wiadomości do rozmiaru kontekstu (<a>Transformacje OpenRouter</a>)", "anthropicApiKey": "Klucz API Zhanlu", "getAnthropicApiKey": "Uzyskaj klucz API Zhanlu", "anthropicUseAuthToken": "Przekaż klucz API Anthropic jako nagłówek Authorization zamiast X-Api-Key", "chutesApiKey": "Klucz API Chutes", "getChutesApiKey": "Uzyskaj klucz API Chutes", "deepSeekApiKey": "Klucz API DeepSeek", "getDeepSeekApiKey": "Uzyskaj klucz API DeepSeek", "moonshotApiKey": "Klucz API Moonshot", "getMoonshotApiKey": "Uzyskaj klucz API Moonshot", "moonshotBaseUrl": "<PERSON><PERSON> Moonshot", "geminiApiKey": "Klucz API Gemini", "getGroqApiKey": "Uzyskaj klucz API Groq", "groqApiKey": "Klucz API Groq", "getGeminiApiKey": "Uzyskaj klucz API Gemini", "getHuggingFaceApiKey": "Uzyskaj klucz API Hugging Face", "huggingFaceApiKey": "Klucz API Hugging Face", "huggingFaceModelId": "ID modelu", "huggingFaceLoading": "Ładowanie...", "huggingFaceModelsCount": "({{count}} modeli)", "huggingFaceSelectModel": "<PERSON><PERSON><PERSON><PERSON> model...", "huggingFaceSearchModels": "Szukaj modeli...", "huggingFaceNoModelsFound": "Nie znaleziono modeli", "huggingFaceProvider": "Dostawca", "huggingFaceProviderAuto": "Automatyczny", "huggingFaceSelectProvider": "<PERSON><PERSON><PERSON><PERSON>...", "huggingFaceSearchProviders": "<PERSON><PERSON>j dostawców...", "huggingFaceNoProvidersFound": "Nie znaleziono dostawców", "apiKey": "Klucz API", "openAiApiKey": "Klucz API OpenAI", "openAiBaseUrl": "URL bazowy", "getOpenAiApiKey": "Uzyskaj klucz API OpenAI", "mistralApiKey": "Klucz API Mistral", "getMistralApiKey": "Uzyskaj klucz API Mistral / Codestral", "codestralBaseUrl": "URL bazowy Codestral (opcjonalnie)", "codestralBaseUrlDesc": "Ustaw opcjonalny URL dla modeli Codestral.", "xaiApiKey": "Klucz API xAI", "getXaiApiKey": "Uzyskaj klucz API xAI", "litellmApiKey": "Klucz API LiteLLM", "litellmBaseUrl": "URL bazowy LiteLLM", "awsCredentials": "Poświadczenia AWS", "awsProfile": "Profil AWS", "awsApiKey": "Klucz API Amazon Bedrock", "awsProfileName": "Nazwa profilu AWS", "awsAccessKey": "Klucz dostępu AWS", "awsSecretKey": "Klucz tajny AWS", "awsSessionToken": "Token sesji AWS", "awsRegion": "Region AWS", "awsCrossRegion": "Użyj wnioskowania międzyregionalnego", "awsBedrockVpc": {"useCustomVpcEndpoint": "Użyj niestandardowego punktu końcowego VPC", "vpcEndpointUrlPlaceholder": "Wprowadź URL punktu końcowego VPC (opcjonalnie)", "examples": "Przykłady:"}, "enablePromptCaching": "Włącz buforowanie podpowiedzi", "enablePromptCachingTitle": "Włącz buforowanie podpowiedzi, aby pop<PERSON><PERSON> wydajność i zmniejszyć koszty dla obsługiwanych modeli.", "cacheUsageNote": "Uwaga: <PERSON><PERSON><PERSON> nie w<PERSON><PERSON> bufora, sp<PERSON><PERSON><PERSON><PERSON> wy<PERSON> inny model, a następnie ponownie wybrać żądany model.", "vscodeLmModel": "<PERSON> <PERSON><PERSON><PERSON>", "vscodeLmWarning": "Uwaga: To bard<PERSON> eksperymentalna integracja, a wsparcie dostawcy może się różnić. <PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON> błąd dotyczący nieobsługiwanego modelu, jest to problem po stronie dostawcy.", "googleCloudSetup": {"title": "<PERSON><PERSON> z Google Cloud Vertex AI, potrzebujesz:", "step1": "1. Utworzyć konto Google Cloud, włączyć API Vertex AI i włączyć żądane modele Zhanlu.", "step2": "2. <PERSON><PERSON><PERSON><PERSON><PERSON> Google Cloud CLI i skonfigurować domyślne poświadczenia aplikacji.", "step3": "3. <PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON> konto usługi z poświadczeniami."}, "googleCloudCredentials": "Poświadczenia Google Cloud", "googleCloudKeyFile": "Ścieżka pliku klucza Google Cloud", "googleCloudProjectId": "ID projektu Google Cloud", "googleCloudRegion": "Region Google Cloud", "lmStudio": {"baseUrl": "URL bazowy (opcjonalnie)", "modelId": "ID modelu", "speculativeDecoding": "Włącz dekodowanie spekulacyjne", "draftModelId": "ID modelu sz<PERSON>", "draftModelDesc": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON> spekulacyjne <PERSON><PERSON>, model s<PERSON><PERSON><PERSON> musi pochodzić z tej samej rodziny modeli.", "selectDraftModel": "Wybierz model s<PERSON><PERSON><PERSON>", "noModelsFound": "Nie znaleziono modeli szkicu. Upewnij się, że LM Studio jest uruchomione z włączonym trybem serwera.", "description": "LM Studio pozwala na lokalne uruchamianie modeli na twoim komputerze. Aby r<PERSON><PERSON>, zapoznaj się z ich <a>przewodnikiem szybkiego startu</a>. Będziesz również musiał uruchomić funkcję <b>serwera lokalnego</b> LM Studio, aby używać go z tym rozszerzeniem. <span>Uwaga:</span> Zhanlu używa złożonych podpowiedzi i działa najlepiej z modelami Zhanlu. Modele o niższych możliwościach mogą nie działać zgodnie z oczekiwaniami."}, "ollama": {"baseUrl": "URL bazowy (opcjonalnie)", "modelId": "ID modelu", "description": "Ollama pozwala na lokalne uruchamianie modeli na twoim komputerze. <PERSON><PERSON>, zapoznaj się z przewodnikiem szybkiego startu.", "warning": "Uwaga: Zhanlu używa złożonych podpowiedzi i działa najlepiej z modelami Zhanlu. Modele o niższych możliwościach mogą nie działać zgodnie z oczekiwaniami."}, "unboundApiKey": "Klucz API Unbound", "getUnboundApiKey": "Uzyskaj klucz API Unbound", "unboundRefreshModelsSuccess": "Lista modeli zaktualizowana! Możesz teraz wybierać spośród najnowszych modeli.", "unboundInvalidApiKey": "Nieprawidłowy klucz API. Sprawdź swój klucz API i spróbuj ponownie.", "humanRelay": {"description": "<PERSON>e jest wymagany klucz API, ale użytkownik będzie musiał pomóc w kopiowaniu i wklejaniu informacji do czatu internetowego AI.", "instructions": "Podczas użytkowania pojawi się okno dialogowe, a bieżąca wiadomość zostanie automatycznie skopiowana do schowka. Będziesz musiał wkleić ją do internetowych wersji AI (takich jak Zhanlu), a następnie skopiować odpowiedź AI z powrotem do okna dialogowego i kliknąć przycisk potwierdzenia."}, "openRouter": {"providerRouting": {"title": "Routing <PERSON><PERSON>wców OpenRouter", "description": "OpenRouter kieruje żądania do najlepszych dostępnych dostawców dla Twojego modelu. Domyślnie żądania są równoważone między najlepszymi dostawcami, aby <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ć czas działania. Możesz jednak wybrać konkretnego dostawcę do użycia z tym modelem.", "learnMore": "Dowiedz się więcej o routingu dostawców"}}, "customModel": {"capabilities": "Skonfiguruj możliwości i ceny swojego niestandardowego modelu zgodnego z OpenAI. Zachowaj ostrożność podczas określania możliwości modelu, ponieważ mogą one wpływać na wydajność Zhanlu.", "maxTokens": {"label": "Maksymalna liczba tokenów wyjściowych", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON> liczba tokenów, które model mo<PERSON><PERSON> wygene<PERSON> w odpowiedzi. (Określ -1, a<PERSON> poz<PERSON><PERSON>ć serwerowi ustawić maksymalną liczbę tokenów.)"}, "contextWindow": {"label": "Roz<PERSON>r okna k<PERSON>", "description": "Cał<PERSON><PERSON><PERSON> l<PERSON> (wejście + wyjście), które model moż<PERSON> przetworzyć."}, "imageSupport": {"label": "Obsługa obrazów", "description": "<PERSON><PERSON> model jest w stanie przetwarzać i rozumieć obrazy?"}, "computerUse": {"label": "Użycie komputera", "description": "<PERSON><PERSON> model jest w stanie wchodzić w interakcję z przeglądarką? (np. <PERSON><PERSON><PERSON>)."}, "promptCache": {"label": "Buforowan<PERSON>zi", "description": "<PERSON><PERSON> model jest w stanie buf<PERSON> podpowiedzi?"}, "pricing": {"input": {"label": "<PERSON><PERSON>", "description": "Koszt za milion tokenów wejściowych/podpowiedzi. Wpływa to na koszt wysyłania kontekstu i instrukcji do modelu."}, "output": {"label": "<PERSON><PERSON>", "description": "Koszt za milion tokenów odpowiedzi modelu. Wpływa to na koszt generowanej treści i uzupełnień."}, "cacheReads": {"label": "<PERSON>na o<PERSON>czytów bufora", "description": "Koszt za milion tokenów za odczyt z bufora. Ta cena jest naliczana przy otrzymywaniu zbuforowanej odpowiedzi."}, "cacheWrites": {"label": "<PERSON>na zap<PERSON> bufora", "description": "Koszt za milion tokenów za zapis do bufora. Ta cena jest naliczana przy pierwszym buforowaniu podpowiedzi."}}, "resetDefaults": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "rateLimitSeconds": {"label": "<PERSON><PERSON>", "description": "Minimalny czas między żądaniami API."}, "consecutiveMistakeLimit": {"label": "Limit błędów i powtórzeń", "description": "Liczba kolejnych błędów lub powtórzonych akcji przed wyświetleniem okna dialogowego 'Roo ma problemy'", "unlimitedDescription": "Włączono nieograniczone próby (automatyczne kontynuowanie). Okno dialogowe nigdy się nie pojawi.", "warning": "⚠️ Ustawienie na 0 pozwala na nieograniczone próby, co może zużyć znaczną ilość API"}, "reasoningEffort": {"label": "Wysiłek rozumowania modelu", "high": "<PERSON><PERSON><PERSON>", "medium": "Średni", "low": "<PERSON><PERSON>"}, "setReasoningLevel": "Włącz wysiłek rozumowania", "claudeCode": {"pathLabel": "Ścieżka Claude Code", "description": "Opcjonalna ścieżka do Twojego CLI Claude Code. Domyślnie 'claude', jeśli nie ustawi<PERSON>.", "placeholder": "Domyślnie: claude", "maxTokensLabel": "Maksymalna liczba tokenów wyjściowych", "maxTokensDescription": "Maksymalna liczba tokenów wyjściowych dla odpowiedzi Claude Code. Domyślnie 8000."}}, "browser": {"enable": {"label": "Włącz narzędzie przeglądarki", "description": "<PERSON><PERSON> wł<PERSON>one, Zhanlu może używać przeglądarki do interakcji ze stronami internetowymi podczas korzystania z modeli obsługujących używanie komputera."}, "viewport": {"label": "Rozmiar viewportu", "description": "Wybierz rozmiar viewportu dla interakcji przeglądarki. Wpływa to na sposób wyświetlania stron internetowych i interakcji z nimi.", "options": {"largeDesktop": "Duży pulpit (1280x800)", "smallDesktop": "Mały pulpit (900x600)", "tablet": "Tablet (768x1024)", "mobile": "Telefon (360x640)"}}, "screenshotQuality": {"label": "Jakość zrzutów ekranu", "description": "Dostosuj jak<PERSON>ć WebP zrzutów ekranu przeglądarki. Wyższe wartości zapewniają wyraźniejsze zrzuty ekranu, ale zwiększają zużycie token."}, "remote": {"label": "Użyj zdalnego połączenia przeglądarki", "description": "Połącz się z przeglądarką Chrome uruchomioną z włączonym zdalnym debugowaniem (--remote-debugging-port=9222).", "urlPlaceholder": "Niestandardowy URL (np. http://localhost:9222)", "testButton": "Testuj połączenie", "testingButton": "Testowanie...", "instructions": "Wprowadź adres hosta protokołu DevTools lub pozostaw puste, aby automatycznie wykryć lokalne instancje Chrome. Przycisk Test Połączenia spróbuje użyć niestandardowego URL, je<PERSON><PERSON> podany, lub automatycznie wykryje, jeśli pole jest puste."}}, "checkpoints": {"enable": {"label": "Włącz automatyczne punkty kontrolne", "description": "<PERSON><PERSON>, Zhanlu automatycznie utworzy punkty kontrolne podczas wykonywania zadań, ułatwiając przeglądanie zmian lub powrót do wcześniejszych stanów."}}, "notifications": {"sound": {"label": "Włącz efekty dźwiękowe", "description": "<PERSON><PERSON>, <PERSON><PERSON><PERSON> będ<PERSON> odtwarzać efekty dźwiękowe dla powiadomień i zdarzeń.", "volumeLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "tts": {"label": "Włącz syntezę mowy", "description": "<PERSON><PERSON> włączone, Zhanlu będzie czytać na głos swoje odpowiedzi za pomocą syntezy mowy.", "speedLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}}, "contextManagement": {"description": "<PERSON><PERSON><PERSON><PERSON><PERSON>, jakie informacje są zawarte w oknie kontekstu AI, wpływając na zużycie token i jakość odpowiedzi", "autoCondenseContextPercent": {"label": "Próg wyzwalający inteligentną kondensację kontekstu", "description": "Gdy okno kontekstu osiągnie ten próg, z<PERSON>lu automatycznie je skondensuje."}, "condensingApiConfiguration": {"label": "Konfiguracja API dla kondensacji kontekstu", "description": "<PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON>rą konfigurację API używać do operacji kondensacji kontekstu. Pozostaw niewybrane, aby użyć aktualnej aktywnej konfiguracji.", "useCurrentConfig": "Domyślna"}, "customCondensingPrompt": {"label": "Niestandardowy monit kondensacji kontekstu", "description": "Niestandardowy monit systemowy dla kondensacji kontekstu. Pozostaw puste, a<PERSON> <PERSON><PERSON><PERSON><PERSON> domyślnego monitu.", "placeholder": "Wprowadź tutaj swój niestandardowy monit kondensacji...\n\nMożesz użyć tej samej struktury co domyślny monit:\n- Poprzednia rozmowa\n- Aktualna praca\n- Kluczowe koncepcje techniczne\n- Istotne pliki i kod\n- Rozwiązywanie problemów\n- Oczekujące zadania i następne kroki", "reset": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hint": "Puste = u<PERSON><PERSON>j domyślnego monitu"}, "autoCondenseContext": {"name": "Automatycznie wyzwalaj inteligentną kondensację kontekstu", "description": "<PERSON><PERSON> w<PERSON>ączone, zhanlu automatycznie skondensuje kontekst po osiągnięciu progu. <PERSON><PERSON> wyłą<PERSON>one, nadal możesz ręcznie wyzwolić kondensację kontekstu."}, "openTabs": {"label": "Limit konteks<PERSON> otwart<PERSON> kart", "description": "Maksymalna liczba otwartych kart VSCode do uwzględnienia w kontekście. Wyższe wartości zapewniają więcej kontekstu, ale zwiększają zużycie token."}, "workspaceFiles": {"label": "Limit kontekstu plików obszaru roboczego", "description": "Maksymalna liczba plików do uwzględnienia w szczegółach bieżącego katalogu roboczego. Wyższe wartości zapewniają więcej kontekstu, ale zwiększają zużycie token."}, "rooignore": {"label": "Pokaż pliki .zhanluignore na listach i w wyszukiwaniach", "description": "Gdy włączone, pliki pasujące do wzorców w .zhanluignore będą pokazywane na listach z symbolem kłódki. Gdy wyłączone, te pliki będą całkowicie ukryte z list plików i wyszukiwań."}, "maxReadFile": {"label": "Próg automatycznego skracania odczytu pliku", "description": "<PERSON><PERSON><PERSON> odczytuje tę liczbę linii, gdy model nie określa wartości początkowej/końcowej. <PERSON><PERSON><PERSON> ta liczba jest mniejsza niż całkowita liczba linii pliku, Zhanlu generuje indeks numerów linii definicji kodu. Przypadki specjalne: -1 nakazuje Zhanlu odczytać cały plik (bez indeksowania), a 0 nakazuje nie czytać żadnych linii i dostarczyć tylko indeksy linii dla minimalnego kontekstu. Niższe wartości minimalizują początkowe użycie kontekstu, umożliwiając późniejsze precyzyjne odczyty zakresów linii. Jawne żądania początku/końca nie są ograniczone tym ustawieniem.", "lines": "linii", "always_full_read": "Z<PERSON>ze czytaj cały plik"}, "maxConcurrentFileReads": {"label": "Limit jednoczesnych odczytów", "description": "Maks<PERSON><PERSON>na liczba plików, które narzędzie 'read_file' może przetwarzać jednocześnie. Wyższe wartości mogą przyspieszyć odczyt wielu małych plików, ale zwiększają zużycie pamięci."}, "diagnostics": {"includeMessages": {"label": "Automatycznie dołączaj diagnostykę do kontekstu", "description": "Gdy włączone, komunikaty diagnostyczne (błędy) z edytowanych plików będą automatycznie dołączane do kontekstu. Zawsze możesz ręcznie dołączyć całą diagnostykę obszaru roboczego używając @problems."}, "maxMessages": {"label": "Maksymalna liczba komunikatów diagnostycznych", "description": "Maksymalna liczba komunikatów diagnostycznych dołączanych na plik. Ten limit dotyczy zarówno automatycznego dołączania (gdy pole wyboru jest włączone) jak i ręcznych wzmianek @problems. Wyższe wartości dostarczają więcej kontekstu, ale zwiększają zużycie tokenów.", "resetTooltip": "Resetuj do wartości domyślnej (50)", "unlimited": "Nieograniczone komunikaty diagnostyczne", "unlimitedLabel": "Nieograniczone"}, "delayAfterWrite": {"label": "Opóźnienie po zapisie, aby um<PERSON>ż<PERSON>wić diagnostyce wykrycie potencjalnych problemów", "description": "Czas oczekiwania po zapisie plików przed kontynuowaniem, aby narzędzia diagnostyczne mogły przetworzyć zmiany i wykryć problemy."}}, "condensingThreshold": {"label": "Próg wyzwalania kondensacji", "selectProfile": "Skonfiguruj próg dla profilu", "defaultProfile": "<PERSON>ny domyślny (wszystkie profile)", "defaultDescription": "<PERSON>dy kontekst osiągnie ten procent, zostanie automatycznie skondensowany dla wszystkich profili, chyba że mają niestandardowe ustawienia", "profileDescription": "Niestandardowy próg tylko dla tego profilu (zastępuje globalny domyślny)", "inheritDescription": "Ten profil dziedziczy globalny domyślny próg ({{threshold}}%)", "usesGlobal": "(używa globalnego {{threshold}}%)"}}, "terminal": {"basic": {"label": "Ustawienia terminala: Podstawowe", "description": "Podstawowe ustawienia terminala"}, "advanced": {"label": "Ustawienia terminala: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Poniższe opcje mogą wymagać ponownego uruchomienia terminala, aby zastosować ustawienie."}, "outputLineLimit": {"label": "Limit wyj<PERSON>cia terminala", "description": "Maksymalna liczba linii do uwzględnienia w wyjściu terminala podczas wykonywania poleceń. Po przekroczeniu linie będą usuwane ze środka, osz<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> token. <0>Dowiedz się więcej</0>"}, "outputCharacterLimit": {"label": "Limit znaków w terminalu", "description": "Maksymalna liczba znaków do uwzględnienia w danych wyjściowych terminala podczas wykonywania poleceń. Limit ten ma pierwszeństwo przed limitem linii, aby zapobiec problemom z pamięcią spowodowanym przez bardzo długie linie. Po przekroczeniu limitu dane wyjściowe zostaną obcięte. <0>Dowiedz się więcej</0>"}, "shellIntegrationTimeout": {"label": "Limit czasu integracji powłoki terminala", "description": "Maksymalny czas oczekiwania na inicjalizację integracji powłoki przed wykonaniem poleceń. Dla użytkowników z długim czasem uruchamiania powłoki, ta wartość może wymagać zwiększenia, jeś<PERSON> widzisz błędy \"Shell Integration Unavailable\" w terminalu. <0>Dowiedz się więcej</0>"}, "shellIntegrationDisabled": {"label": "Wyłącz integrację powłoki terminala", "description": "<PERSON><PERSON><PERSON><PERSON> tę opcję, je<PERSON>li polecenia terminala nie działają poprawnie lub wid<PERSON><PERSON> błędy 'Shell Integration Unavailable'. Używa to prosts<PERSON>j metody urucha<PERSON> pole<PERSON>ń, omi<PERSON><PERSON><PERSON><PERSON> niektóre zaawansowane funkcje terminala. <0>Dowiedz się więcej</0>"}, "commandDelay": {"label": "Opóźnienie poleceń terminala", "description": "Opóźnienie w milisekundach dodawane po wykonaniu polecenia. Domyślne ustawienie 0 całkowicie wyłącza opóźnienie. <PERSON><PERSON>e to pomóc w zapewnieniu pełnego przechwytywania wyjścia poleceń w terminalach z problemami z synchronizacją. W większości terminali jest to implementowane przez ustawienie `PROMPT_COMMAND='sleep N'`, a PowerShell dodaje `start-sleep` na końcu każdego polecenia. Pierwotnie było to obejście błędu VSCode#237208 i może nie być potrzebne. <0>Dowiedz się więcej</0>"}, "compressProgressBar": {"label": "Kompresuj wyjście pasków postępu", "description": "Po włączeniu, prz<PERSON><PERSON>za wyjście terminala z powrotami karetki (\\r), aby sym<PERSON><PERSON><PERSON> sposób wyświetlania treści przez prawdziwy terminal. Usuwa to pośrednie stany pasków postępu, zachowując tylko stan końcowy, co oszczędza przestrzeń kontekstową dla bardziej istotnych informacji. <0>Dowiedz się więcej</0>"}, "powershellCounter": {"label": "Włącz obejście licznika PowerShell", "description": "Po włączeniu dodaje licznik do poleceń PowerShell, aby z<PERSON><PERSON><PERSON> prawidłowe wykonanie poleceń. Pomaga to w terminalach PowerShell, które mogą mieć problemy z przechwytywaniem wyjścia. <0>Dowiedz się więcej</0>"}, "zshClearEolMark": {"label": "Wyczyść znacznik końca linii ZSH", "description": "Po włączeniu czyści znacznik końca linii ZSH poprzez ustawienie PROMPT_EOL_MARK=''. Zapobiega to problemom z interpretacją wyjścia poleceń, gdy kończy się ono znakami specjalnymi jak '%'. <0>Dowiedz się więcej</0>"}, "zshOhMy": {"label": "Włącz integrację Oh My Zsh", "description": "Po włączeniu ustawia ITERM_SHELL_INTEGRATION_INSTALLED=Yes, aby włączyć funkcje integracji powłoki Oh My Zsh. Zastosowanie tego ustawienia może wymagać ponownego uruchomienia IDE. <0>Dowiedz się więcej</0>"}, "zshP10k": {"label": "Włącz integrację Powerlevel10k", "description": "Po włączeniu ustawia POWERLEVEL9K_TERM_SHELL_INTEGRATION=true, aby włączyć funkcje integracji powłoki Powerlevel10k. <0>Dowiedz się więcej</0>"}, "zdotdir": {"label": "Włącz obsługę ZDOTDIR", "description": "Po włączeniu tworzy tymczasowy katalog dla ZDOTDIR, aby poprawnie obsłużyć integrację powłoki zsh. Zapewnia to prawidłowe działanie integracji powłoki VSCode z zsh, zachowując twoją konfigurację zsh. <0>Dowiedz się więcej</0>"}, "inheritEnv": {"label": "Dziedzicz zmienne środowiskowe", "description": "Po włączeniu terminal dziedziczy zmienne środowiskowe z procesu nadrzędnego VSCode, takie jak ustawienia integracji powłoki zdefiniowane w profilu użytkownika. Przełącza to bezpośrednio globalne ustawienie VSCode `terminal.integrated.inheritEnv`. <0>Dowiedz się więcej</0>"}}, "advancedSettings": {"title": "Ustawi<PERSON>e"}, "advanced": {"diff": {"label": "Włącz edycję przez różnice", "description": "<PERSON><PERSON>, Zhanlu będzie w stanie edytować pliki szybciej i automatycznie odrzuci obcięte pełne zapisy plików. Działa najlepiej z najnowszym modelem Zhanlu.", "strategy": {"label": "Strategia diff", "options": {"standard": "Standardowa (Pojedynczy blok)", "multiBlock": "Eksperymentalna: <PERSON><PERSON>", "unified": "Eksperymentalna: Diff ujednolicony"}, "descriptions": {"standard": "Standardowa strategia diff stosuje zmiany do jednego bloku kodu na raz.", "unified": "Strategia diff ujednoliconego stosuje wiele podejść do zastosowania różnic i wybiera najlepsze podejście.", "multiBlock": "Strategia diff wieloblokowego pozwala na aktualizację wielu bloków kodu w pliku w jednym żądaniu."}}, "matchPrecision": {"label": "Precyzja dopasowania", "description": "Ten suwak kontroluje, jak dokładnie sekcje kodu muszą pasować podczas stosowania różnic. Niższe wartości umożliwiają bardziej elastyczne dopasowywanie, ale zwiększają ryzyko <PERSON> zamian. Używaj wartości poniżej 100% z najwyższą ostrożnością."}}, "todoList": {"label": "Włącz narzędzie listy zadań", "description": "Po włączeniu zhanlu może tworzyć i zarządzać listami zadań do śledzenia postępu zadań. Pomaga to organizować złożone zadania w łatwe do zarządzania kroki."}}, "completion": {"description": "Skonfiguruj ustawienia uzupełniania kodu, aby <PERSON><PERSON> swoją pracę.", "configureButton": "Skonfiguruj", "debounceTime": {"label": "Opóźnienie wyzwalacza uzupełniania", "description": "<PERSON>zas opóźnienia dla wyzwalacza uzupełniania kodu (milisekundy)"}, "number": {"label": "Liczba uzupełnień", "description": "Liczba kandydatów uzupełniania kodu do wygenerowania"}, "granularity": {"label": "Preferencja szczegółowości uzupełniania", "description": "Ustawienie preferencji szczegółowości dla uzupełniania kodu", "singleRow": "Pojedyncza linia", "oneTimeMaximization": "Jednorazowa maksymalizacja", "balanced": "Zrównoważony"}, "multipleLineCompletion": {"label": "Tryb uzupełniania wieloliniowego", "description": "Tryb wyzwalacza dla uzupełniania kodu wieloliniowego", "autoCompletion": "Automatyczne uzupełnianie", "triggerCompletion": "Uzupełnianie wyzwalane"}, "maxTokens": {"label": "Maksymalna liczba tokenów", "description": "Maksymalna liczba tokenów dla uzupełniania kodu"}}, "experimental": {"DIFF_STRATEGY_UNIFIED": {"name": "Użyj eksperymentalnej ujednoliconej strategii diff", "description": "Włącz eksperymentalną ujednoliconą strategię diff. Ta strategia może zmniejszyć liczbę ponownych prób spowodowanych błędami modelu, ale może powodować nieoczekiwane zachowanie lub nieprawidłowe edycje. Włącz tylko jeśli rozumiesz ryzyko i jesteś gotów dokładnie przeglądać wszystkie zmiany."}, "SEARCH_AND_REPLACE": {"name": "Użyj eksperymentalnego narzędzia do wyszukiwania i zamiany", "description": "Włącz eksperymentalne narzędzie do wyszukiwania i zamiany, umożliwiając Zhanlu zastąpienie wielu wystąpień wyszukiwanego terminu w jednym żądaniu."}, "INSERT_BLOCK": {"name": "Użyj eksperymentalnego narzędzia do wstawiania treści", "description": "Włącz eksperymentalne narzędzie do wstawiania treści, umożliwiając Zhanlu wstawianie treści w określonych numerach linii bez konieczności tworzenia diff."}, "POWER_STEERING": {"name": "Użyj eksperymentalnego trybu \"wspomagania kierownicy\"", "description": "Po włączeniu, Zhanlu będzie częściej przypominać modelowi o szczegółach jego bieżącej definicji trybu. <PERSON><PERSON><PERSON><PERSON><PERSON> to do silniejszego przestrzegania definicji ról i niestandardowych instrukcji, ale będzie używać więcej tokenów na wiadomość."}, "MULTI_SEARCH_AND_REPLACE": {"name": "Użyj eksperymentalnego narzędzia diff wieloblokowego", "description": "Po włączeniu, Zhanlu użyje narzędzia diff wieloblokowego. Spróbuje to zaktualizować wiele bloków kodu w pliku w jednym żądaniu."}, "CONCURRENT_FILE_READS": {"name": "Włącz jednoczesne odczytywanie plików", "description": "Po włączeniu zhanlu może odczytać wiele plików w jednym żądaniu. Po wyłączeniu zhanlu musi odczytywać pliki pojedynczo. Wyłączenie może pomóc podczas pracy z mniej wydajnymi modelami lub gdy chcesz mieć większą kontrolę nad dostępem do plików."}, "MARKETPLACE": {"name": "Włącz Marketplace", "description": "<PERSON><PERSON>, m<PERSON><PERSON><PERSON><PERSON> instalować MCP i niestandardowe tryby z Marketplace."}, "MULTI_FILE_APPLY_DIFF": {"name": "Włącz równoczesne edycje plików", "description": "<PERSON><PERSON> wł<PERSON>czone, zhanlu może edytować wiele plików w jednym żądaniu. <PERSON><PERSON> wyłączone, zhanlu musi edytować pliki jeden po drugim. Wyłączenie tego może pomóc podczas pracy z mniej zdolnymi modelami lub gdy chcesz mieć większą kontrolę nad modyfikacjami plików."}}, "promptCaching": {"label": "Wyłącz buforowanie promptów", "description": "<PERSON>, <PERSON><PERSON><PERSON> nie będzie uży<PERSON>ć buforowania promptów dla tego modelu."}, "temperature": {"useCustom": "Użyj niestandardowej temperatury", "description": "Kontrol<PERSON><PERSON> w odpowiedziach modelu.", "rangeDescription": "Wyższe wartości sprawiają, że wyjście jest bardziej los<PERSON>e, niższe wartości czynią je bardziej deterministycznym."}, "modelInfo": {"supportsImages": "Obsługuje obrazy", "noImages": "Nie obsługuje obrazów", "supportsComputerUse": "Obsługuje użycie komputera", "noComputerUse": "Nie obsługuje użycia komputera", "supportsPromptCache": "Obsługuje buforowanie podpowiedzi", "noPromptCache": "Nie obsługuje buforowania podpowiedzi", "maxOutput": "Maksymalne wyjście", "inputPrice": "<PERSON><PERSON>", "outputPrice": "<PERSON><PERSON>", "cacheReadsPrice": "<PERSON>na o<PERSON>czytów bufora", "cacheWritesPrice": "<PERSON>na zap<PERSON> bufora", "enableStreaming": "Włącz strumieniowanie", "enableR1Format": "Włącz parametry modelu R1", "enableR1FormatTips": "Należy włączyć podczas korzystania z modeli R1, takich jak QW<PERSON>, aby un<PERSON><PERSON><PERSON><PERSON> bł<PERSON><PERSON> 400", "useAzure": "Użyj Azure", "azureApiVersion": "Ustaw wersję API Azure", "gemini": {"freeRequests": "* Darmowe do {{count}} zapytań na minutę. Po tym, rozliczanie zależy od rozmiaru podpowiedzi.", "pricingDetails": "Więcej informacji znajdziesz w szczegółach cennika.", "billingEstimate": "* Rozliczenie jest szacunkowe - dokładny koszt zależy od rozmiaru podpowiedzi."}}, "modelPicker": {"automaticFetch": "Rozszerzenie automatycznie pobiera najnowszą listę modeli dostępnych w <serviceLink>{{serviceName}}</serviceLink>. <PERSON><PERSON><PERSON> nie jeste<PERSON> pewien, kt<PERSON>ry model wy<PERSON><PERSON>, <PERSON><PERSON><PERSON> działa najlepiej z <defaultModelLink>{{defaultModelId}}</defaultModelLink>. Możesz również wyszuka<PERSON> \"free\", aby znaleźć obecnie dostępne opcje bezpłatne.", "label": "Model", "searchPlaceholder": "Wyszukaj", "noMatchFound": "Nie znaleziono dopasowań", "useCustomModel": "Użyj niestandardowy: {{modelId}}"}, "footer": {"feedback": "<PERSON><PERSON><PERSON> masz p<PERSON> lub uwagi, nie wa<PERSON>j si<PERSON> <qqDocsLink>zg<PERSON><PERSON><PERSON> problem</qqDocsLink>.", "version": "<PERSON><PERSON><PERSON> v{{version}}", "telemetry": {"label": "Zezwól na anonimowe raportowanie błędów i użycia", "description": "<PERSON><PERSON><PERSON><PERSON>, wys<PERSON><PERSON>jąc anonimowe dane o użytkowaniu i raporty o błędach. Nigdy nie są wysyłane kod, podpowiedzi ani informacje osobiste. Zobacz naszą politykę prywat<PERSON>ści, aby uzyskać więcej szczegółów."}, "settings": {"import": "Import<PERSON>j", "export": "Eksportuj", "reset": "<PERSON><PERSON><PERSON><PERSON>"}}, "thinkingBudget": {"maxTokens": "Maksymalna liczba tokenów", "maxThinkingTokens": "Maksymalna liczba tokenów myślenia"}, "validation": {"apiKey": "Musisz podać prawidłowy klucz API.", "awsRegion": "Musisz wybrać region, aby korzystać z Amazon Bedrock.", "googleCloud": "Musisz podać prawidłowe ID projektu i region Google Cloud.", "modelId": "Musisz podać prawidłowe ID modelu.", "modelSelector": "<PERSON><PERSON>z podać prawidłowy selektor modelu.", "openAi": "Musisz podać prawidłowy bazowy URL, klucz API i ID modelu.", "arn": {"invalidFormat": "Nieprawidłowy format ARN. Sprawdź wymagania dotyczące formatu.", "regionMismatch": "Ostrzeżenie: Region w Twoim ARN ({{arnRegion}}) nie zgadza się z wybranym regionem ({{region}}). Może to powodować problemy z dostępem. Dostawca użyje regionu z ARN."}, "modelAvailability": "Podane ID modelu ({{modelId}}) jest niedostępne. Wybierz inny model.", "providerNotAllowed": "Dostawca '{{provider}}' nie jest dozwolony przez Twoją organizację", "modelNotAllowed": "Model '{{model}}' nie jest dozwolony dla dostawcy '{{provider}}' przez <PERSON> organizację", "profileInvalid": "Ten profil zaw<PERSON> dostawcę lub model, kt<PERSON><PERSON> nie jest dozwolony przez Twoją organizację"}, "placeholders": {"apiKey": "Wprowadź klucz API...", "profileName": "Wprowadź nazwę profilu", "accessKey": "Wprowadź klucz dostępu...", "secretKey": "Wprowadź klucz tajny...", "sessionToken": "Wprowadź token sesji...", "credentialsJson": "Wprowadź JSON poświadczeń...", "keyFilePath": "Wprowadź ścieżkę pliku klucza...", "projectId": "Wprowadź ID projektu...", "customArn": "Wprowadź ARN (np. arn:aws:bedrock:us-east-1:123456789012:foundation-model/my-model)", "baseUrl": "Wprowadź podstawowy URL...", "modelId": {"lmStudio": "np. meta-llama-3.1-8b-instruct", "lmStudioDraft": "np. lmstudio-community/llama-3.2-1b-instruct", "ollama": "np. llama3.1"}, "numbers": {"maxTokens": "np. 4096", "contextWindow": "np. 128000", "inputPrice": "np. 0.0001", "outputPrice": "np. 0.0002", "cacheWritePrice": "np. 0.00005"}}, "defaults": {"ollamaUrl": "Domyślnie: http://localhost:11434", "lmStudioUrl": "Domyślnie: http://localhost:1234", "geminiUrl": "Domyślnie: https://generativelanguage.googleapis.com"}, "labels": {"customArn": "Niestandardowy ARN", "useCustomArn": "Użyj niestandardowego ARN..."}, "includeMaxOutputTokens": "Uwzględnij maksymalne tokeny wyjściowe", "includeMaxOutputTokensDescription": "Wyślij parametr maksymalnych tokenów wyjściowych w żądaniach API. Niektórzy dostawcy mogą tego nie obsługiwać.", "limitMaxTokensDescription": "Ogranicz maksymalną liczbę tokenów w odpowiedzi", "maxOutputTokensLabel": "Ma<PERSON><PERSON>alne tokeny wyjściowe", "maxTokensGenerateDescription": "Maksymalne tokeny do wygenerowania w odpowiedzi"}